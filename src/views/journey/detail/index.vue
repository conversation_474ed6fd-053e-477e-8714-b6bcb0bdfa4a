<script setup lang="ts">
import NotAdd from './components/NotAdd.vue'
import { addMyJourney, get<PERSON>our<PERSON>, remove<PERSON>y<PERSON><PERSON><PERSON> } from '@/api/journey'
import type { JourneyDetail, Course } from '@/api/journey'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { SuperBreadcrumb } from '@/components/common/SuperBreadcrumb'
import { Progress } from '@/components/ui/progress'
import {
  ArrowLeft,
  Info,
  Calendar,
  User,
  BookOpen,
  Clock,
  Users,
  Play,
  CheckCircle,
  Plus,
  Minus,
  Star,
  Target,
  Award
} from 'lucide-vue-next'
import { useRouter, useRoute } from 'vue-router'
import LazyImage from '@/components/LazyImage.vue'
const route = useRoute()
const router = useRouter()
const detail = ref<JourneyDetail>()
const loading = ref(true)
const isReadOnly = ref(false)
const isShowReadOnly = ref(false)

const journeyId = route.params.id
const steps = ref() // 进度条长度
const message = useMessage()

// Format date time
const formatDateTime = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Get course status
const getCourseStatus = (course: Course) => {
  if (course.studyStatus === 3) return 'Completed'
  if (course.studyStatus === 1) return 'In Progress'
  return 'Not Started'
}

// Get course status variant
const getCourseStatusVariant = (course: Course) => {
  if (course.studyStatus === 3) return 'outline'
  if (course.studyStatus === 1) return 'default'
  return 'secondary'
}

// Calculate progress percentage
const getProgressPercentage = computed(() => {
  if (!detail.value?.courseList?.length) return 0
  const completedCourses = detail.value.courseList.filter(
    (course) => course.studyStatus === 3
  ).length
  return Math.round((completedCourses / detail.value.courseList.length) * 100)
})

// Go back to journey list
const goBack = () => {
  router.push('/journey')
}

// Format duration from seconds to hours
const formatDurationHours = (seconds: number) => {
  if (!seconds) return 0
  const hours = Math.floor(seconds / 3600)
  return hours || Math.ceil(seconds / 3600) // 如果小于1小时，向上取整显示1小时
}

// Format duration from seconds to minutes:seconds
const formatDurationMinutes = (seconds: number) => {
  if (!seconds) return '0:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${String(remainingSeconds).padStart(2, '0')}`
}
/**
 * 获取学习地图详细信息
 */
const getDetail = async () => {
  try {
    loading.value = true
    console.log('Fetching journey detail for ID:', journeyId)
    const res = await getJourney(Number(journeyId))
    console.log('Journey detail API response:', res)
    console.log('CourseList length:', res?.courseList?.length)
    console.log('CourseCount from API:', res?.courseCount)

    detail.value = res
    // 如果type不为null，代表已经add to my journey了
    isReadOnly.value = res.type !== null
    // 控制添加学习地图展示不展示,  0展示,1不展示
    isShowReadOnly.value = (res.type !== null && res.type === 0) || res.type === null

    console.log('Journey type:', res.type)
    console.log('isReadOnly (已添加到我的学习路径):', isReadOnly.value)
    console.log('isShowReadOnly (显示操作按钮):', isShowReadOnly.value)

    // 处理课程列表
    if (detail.value?.courseList && Array.isArray(detail.value.courseList)) {
      console.log('Original courseList:', detail.value.courseList)
      console.log('Original courseList length:', detail.value.courseList.length)

      // 将已下架的课程过滤掉 (status !== 0)
      const filteredCourses = res.courseList.filter((item: Course) => item.status !== 0)
      console.log('Filtered courseList length:', filteredCourses.length)
      detail.value.courseList = filteredCourses

      // 0: 未完成 1: 进行中 3：已完成
      let foundIncomplete = false // 标记是否找到第一个未完成的课程

      detail.value?.courseList.forEach((item: Course, index: number) => {
        console.log(`Course ${index + 1}:`, {
          name: item.name,
          studyStatus: item.studyStatus,
          status: item.status,
          handDuration: item.handDuration
        })

        if (item.studyStatus === 0 || item.studyStatus === 1) {
          // 查询到第一个未学习完成的课程,后面如果还有未学习的课程不用管,默认先让去学习第一个课程
          if (!foundIncomplete) {
            item.isComplete = true
            foundIncomplete = true
          } else {
            item.isComplete = false // 后面未完成的课程不在提示
          }
        } else if (item.studyStatus === 3) {
          item.isComplete = true
        }
      })

      console.log('Final processed courseList:', detail.value?.courseList)
      console.log('Final courseList length:', detail.value?.courseList?.length)

      // 通过最后一个状态为已完成的下标展示进度条(在获取到下标的基础上面需要+1)
      steps.value =
        detail.value?.courseList.findLastIndex((item: Course) => item.studyStatus === 3) + 1
    } else {
      console.log('No courseList found or courseList is not an array')
      console.log('detail.value?.courseList:', detail.value?.courseList)
    }
  } catch (error) {
    console.error('Failed to load journey detail:', error)
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await getDetail()
})

const handleAddJourney = async () => {
  const isOperation = await message.confirm(
    isReadOnly.value
      ? 'Are you sure you want to remove this journey from your learning path?'
      : 'Are you sure you want to add this journey to your learning path?'
  )
  if (isOperation) {
    loading.value = true
    try {
      const api = isReadOnly.value ? removeMyJourney : addMyJourney
      await api(Number(journeyId))
      // 切换状态：如果当前已添加(type !== null)，操作后变为未添加(type = null)
      // 如果当前未添加(type === null)，操作后变为已添加(type !== null)
      isReadOnly.value = !isReadOnly.value
      // 更新detail.value.type以反映新状态
      if (detail.value) {
        detail.value.type = isReadOnly.value ? 0 : null
      }
      console.log('Journey operation completed. New isReadOnly state:', isReadOnly.value)
    } catch (error) {
      console.error('Failed to update journey:', error)
    } finally {
      loading.value = false
    }
  }
}
function handleClick(item: Course) {
  // 如果是已经完成的课程，跳转到课程详情
  if (item.isComplete) {
    router.push({ name: 'ContentDetail', params: { id: item.id } })
  }
}
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <!-- Header -->
        <template #header>
          <div class="flex items-center px-4 gap-3 w-full h-full">
            <!-- Left Section: Breadcrumb -->
            <div class="flex items-center gap-3">
              <SuperBreadcrumb
                current-title="Journey Detail"
                :show-back-button="true"
              />
              <!-- Journey Status Badge -->
              <Badge
                v-if="detail"
                :variant="isReadOnly ? 'default' : 'secondary'"
                class="text-xs font-medium"
              >
                <component :is="isReadOnly ? CheckCircle : Plus" class="w-3 h-3 mr-1" />
                {{ isReadOnly ? 'In My Journey' : 'Available' }}
              </Badge>
            </div>

            <!-- Center Section: Title -->
            <div class="flex-1 flex justify-center">
              <div v-if="detail" class="text-center">
                <h1 class="text-lg font-semibold text-gray-900">{{ detail.title }}</h1>
              </div>
            </div>

            <!-- Right Section: Action Buttons -->
            <div class="flex items-center gap-2">
              <!-- Add/Remove Journey Button -->
              <Button
                v-if="isShowReadOnly"
                @click="handleAddJourney"
                :variant="isReadOnly ? 'outline' : 'default'"
                size="sm"
                class="text-xs"
              >
                <component :is="isReadOnly ? Minus : Plus" class="w-4 h-4 mr-1" />
                {{ isReadOnly ? 'Remove from My Journey' : 'Add to My Journey' }}
              </Button>
            </div>
          </div>
        </template>

        <!-- Content Area -->
        <div class="p-6">
          <!-- Loading state -->
          <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
            <div class="flex flex-col items-center space-y-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span class="text-gray-600 text-lg">Loading journey details...</span>
            </div>
          </div>

          <!-- Journey Details -->
          <div v-else-if="detail">
            <!-- Journey Overview -->
            <Card>
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <Info class="w-5 h-5 mr-2" />
                  Journey Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <!-- Journey Image -->
                  <div class="lg:col-span-1">
                    <div
                      class="aspect-video rounded-lg overflow-hidden bg-gradient-to-br"
                    >
                      <LazyImage
                        :src="detail.cover"
                        :alt="detail.title"
                        :aspect-ratio="'16/9'"
                        class="w-full h-full object-cover"
                      />
                    </div>
                  </div>

                  <!-- Journey Info -->
                  <div class="lg:col-span-2 space-y-4">
                    <!-- Description -->
                    <div v-if="detail.introduction">
                      <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                        >Description</Label
                      >
                      <div class="mt-2 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <p class="text-gray-700 leading-relaxed">{{ detail.introduction }}</p>
                      </div>
                    </div>

                    <!-- Journey Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <!-- Course Count -->
                      <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <BookOpen class="w-6 h-6 mx-auto text-blue-600 mb-1" />
                        <div class="text-lg font-semibold text-blue-900">{{
                          detail.courseList?.length || detail.courseCount || 0
                        }}</div>
                        <div class="text-xs text-blue-600">Courses</div>
                      </div>

                      <!-- Progress -->
                      <div class="text-center p-3 bg-green-50 rounded-lg">
                        <Target class="w-6 h-6 mx-auto text-green-600 mb-1" />
                        <div class="text-lg font-semibold text-green-900"
                          >{{ getProgressPercentage }}%</div
                        >
                        <div class="text-xs text-green-600">Progress</div>
                      </div>

                      <!-- Category -->
                      <div
                        v-if="detail.categoryName"
                        class="text-center p-3 bg-purple-50 rounded-lg"
                      >
                        <Award class="w-6 h-6 mx-auto text-purple-600 mb-1" />
                        <div class="text-sm font-semibold text-purple-900 truncate">{{
                          detail.categoryName
                        }}</div>
                        <div class="text-xs text-purple-600">Category</div>
                      </div>

                      <!-- My Journey Status -->
                      <div
                        class="text-center p-3"
                        :class="isReadOnly ? 'bg-green-50' : 'bg-orange-50'"
                      >
                        <component
                          :is="isReadOnly ? CheckCircle : Plus"
                          class="w-6 h-6 mx-auto mb-1"
                          :class="isReadOnly ? 'text-green-600' : 'text-orange-600'"
                        />
                        <div
                          class="text-sm font-semibold"
                          :class="isReadOnly ? 'text-green-900' : 'text-orange-900'"
                        >
                          {{ isReadOnly ? 'Added' : 'Available' }}
                        </div>
                        <div
                          class="text-xs"
                          :class="isReadOnly ? 'text-green-600' : 'text-orange-600'"
                          >My Journey</div
                        >
                      </div>
                    </div>

                    <!-- Progress Bar -->
                    <div v-if="detail.courseList?.length">
                      <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                        >Overall Progress</Label
                      >
                      <div class="mt-2">
                        <Progress :value="getProgressPercentage" class="h-2" />
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                          <span
                            >{{
                              detail.courseList.filter((c) => c.studyStatus === 3).length
                            }}
                            completed</span
                          >
                          <span>{{ detail.courseList.length }} total courses</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <!-- Course List -->
            <Card class="mt-8">
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <BookOpen class="w-5 h-5 mr-2" />
                  Learning Path
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div v-if="detail.courseList?.length > 0" class="space-y-4">
                  <div
                    v-for="(course, index) in detail.courseList"
                    :key="course.id"
                    class="group relative flex items-center p-4 border rounded-lg transition-all duration-200"
                    :class="[
                      course.isComplete
                        ? 'border-primary/20 bg-primary/5 hover:bg-primary/10 cursor-pointer hover:shadow-md'
                        : 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'
                    ]"
                    @click="handleClick(course)"
                  >
                    <!-- Step Number -->
                    <div class="flex-shrink-0 mr-4">
                      <div
                        class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold"
                        :class="[
                          course.studyStatus === 3
                            ? 'bg-green-100 text-green-700'
                            : course.studyStatus === 1
                              ? 'bg-blue-100 text-blue-700'
                              : 'bg-gray-100 text-gray-500'
                        ]"
                      >
                        <CheckCircle v-if="course.studyStatus === 3" class="w-4 h-4" />
                        <Play v-else-if="course.studyStatus === 1" class="w-4 h-4" />
                        <span v-else>{{ index + 1 }}</span>
                      </div>
                    </div>

                    <!-- Course Image -->
                    <div class="flex-shrink-0 mr-4">
                      <div
                        class="w-16 h-16 rounded-lg overflow-hidden bg-gradient-to-br from-slate-100 to-slate-200"
                      >
                        <LazyImage
                          :src="course.cover"
                          :alt="course.name"
                          :aspect-ratio="'1'"
                          class="w-full h-full object-cover"
                        />
                      </div>
                    </div>

                    <!-- Course Info -->
                    <div class="flex-1 min-w-0">
                      <div class="flex items-start justify-between">
                        <div class="flex-1 min-w-0">
                          <h4
                            class="text-sm font-semibold text-gray-900 line-clamp-1 group-hover:text-primary transition-colors"
                          >
                            {{ course.name }}
                          </h4>
                          <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <div v-if="course.handDuration" class="flex items-center">
                              <Clock class="w-3 h-3 mr-1" />
                              <span>{{ formatDurationHours(course.handDuration) }} hours</span>
                            </div>
                            <div v-else-if="course.duration" class="flex items-center">
                              <Clock class="w-3 h-3 mr-1" />
                              <span>{{ formatDurationMinutes(course.duration) }}</span>
                            </div>
                            <div v-if="course.enrollNumber" class="flex items-center">
                              <Users class="w-3 h-3 mr-1" />
                              <span>{{ course.enrollNumber }} learners</span>
                            </div>
                          </div>
                        </div>

                        <!-- Status Badge -->
                        <div class="flex-shrink-0 ml-4">
                          <Badge :variant="getCourseStatusVariant(course)" class="text-xs">
                            {{ getCourseStatus(course) }}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <!-- Action Indicator -->
                    <div v-if="course.isComplete" class="flex-shrink-0 ml-4">
                      <ArrowLeft
                        class="w-4 h-4 text-primary rotate-180 opacity-0 group-hover:opacity-100 transition-opacity"
                      />
                    </div>

                    <!-- Connecting Line -->
                    <div
                      v-if="index < detail.courseList.length - 1"
                      class="absolute left-8 top-16 w-0.5 h-4 bg-gray-200"
                    />
                  </div>
                </div>

                <!-- Empty State -->
                <div v-else class="text-center py-12">
                  <div
                    class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <BookOpen class="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 class="text-sm font-medium text-gray-900 mb-1">No Courses Available</h3>
                  <p class="text-sm text-gray-500"
                    >This learning journey doesn't have any courses yet.</p
                  >
                </div>

                <!-- Not Added State -->
                <div v-if="!isReadOnly" class="mt-8 pt-6 border-t border-gray-200">
                  <NotAdd @add="handleAddJourney" />
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Error state -->
          <div v-else class="flex items-center justify-center min-h-[400px]">
            <div class="text-center text-gray-500">
              <div
                class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4 mx-auto"
              >
                <BookOpen class="w-12 h-12 text-slate-400" />
              </div>
              <h3 class="text-lg font-semibold text-slate-900 mb-2">Journey Not Found</h3>
              <p class="text-slate-500 max-w-md">
                The learning journey you're looking for doesn't exist or has been removed.
              </p>
            </div>
          </div>
        </div>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
// Modern styles are handled by Tailwind CSS and component classes
</style>
