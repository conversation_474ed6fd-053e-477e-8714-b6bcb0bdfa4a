<script setup lang="ts" name="Journey">
import JourneyGrid from './components/JourneyGrid.vue'
import { listJourney, getJourneyCategoryAll } from '@/api/journey'
import { Button } from '@/components/ui/button'
import { RefreshCcw, Map, Route, BookOpen, Folder } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { SmartPagination } from '@/components/SmartPagination'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  type SidebarProps,
} from '@/components/ui/sidebar'
import { Label } from '@/components/ui/label'

// Props for Sidebar
const props = defineProps<SidebarProps>()

interface JourneyItem {
  completed: boolean
  courseCount: number
  courseDuration: string
  courseIds: Array<string>
  cover: string
  id: number
  introduction: string
  keywords: string
  status: number
  studentCount: number
  title: string
}

const journeyList = ref<Array<JourneyItem>>([])
const queryParams = ref({
  title: '',
  categoryId: 'all', // 默认为"全部分类"
  pageNum: 1,
  pageSize: 20
})
const total = ref(0)
const loading = ref(false)
// Learning journey category list
const journeyCategoryList = ref([])
const router = useRouter()
const getDataList = async () => {
  const params = {
    ...queryParams.value
  }
  // Clean up undefined values and handle "all" category
  if (!params.categoryId || params.categoryId === 'all') {
    delete params.categoryId
  }
  if (!params.title) {
    delete params.title
  }

  try {
    loading.value = true
    const data = await listJourney(params)
    console.log('Journey API response:', data)
    if (data && data.list) {
      journeyList.value = data.list
      total.value = data.total || data.list.length
    } else {
      journeyList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('Failed to load journey list:', error)
    journeyList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.value.pageNum = 1
  queryParams.value.pageSize = 20
  getDataList()
}

const handleReset = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNum = 1
  queryParams.value.title = ''
  queryParams.value.categoryId = 'all' // 重置为"全部分类"
  handleSearch()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
  getDataList()
}

// Handle category navigation
const handleCategoryNavigation = (categoryId: string | number) => {
  queryParams.value.categoryId = categoryId
  queryParams.value.pageNum = 1 // Reset to first page
  getDataList()
}

// Handle journey click
const handleJourneyClick = (journey: JourneyItem) => {
  router.push({ name: 'JourneyDetail', params: { id: journey.id } })
}

// Get learning journey categories
const getListCategory = async () => {
  try {
    const res = await getJourneyCategoryAll()
    journeyCategoryList.value = res
  } catch (error) {
    console.error('Failed to load categories:', error)
  }
}
onMounted(() => {
  getDataList()
  getListCategory()
})
</script>

<template>
  <ContainerWrapper :autoCollapse="true">
    <!-- Left Navigation -->
    <template #nav>
      <Sidebar v-bind="props" :collapsible="'none'" class="border-r-0 w-full bg-white">
        <SidebarHeader class="border-b-0">
          <div class="flex items-center gap-3 px-2 py-3">
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <Map class="size-4" />
            </div>
            <div class="flex flex-col gap-0 leading-none">
              <span class="font-semibold text-sidebar-foreground">Learning Journeys</span>
              <span class="text-xs text-sidebar-foreground/60">Learning Platform</span>
            </div>
          </div>
        </SidebarHeader>

        <SidebarContent class="px-0 overflow-y-auto">
          <!-- Categories Section -->
          <SidebarGroup>
            <SidebarGroupLabel>Categories</SidebarGroupLabel>
            <SidebarGroupContent class="px-0">
              <SidebarMenu>
                <!-- All Categories -->
                <SidebarMenuItem>
                  <SidebarMenuButton
                    class="w-full"
                    @click="handleCategoryNavigation('all')"
                    :is-active="queryParams.categoryId === 'all'"
                  >
                    <Folder class="size-4" />
                    <span>All Categories</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <!-- Dynamic Categories from API -->
                <SidebarMenuItem v-for="category in journeyCategoryList" :key="category.id">
                  <SidebarMenuButton
                    @click="handleCategoryNavigation(category.id)"
                    :is-active="queryParams.categoryId === category.id"
                  >
                    <Route class="size-4" />
                    {{ category.title }}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </template>

    <!-- Main Content Area -->
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="px-4 flex items-center justify-between gap-4 w-full h-full">
            <!-- Left: Search Box -->
            <SuperSearch
              v-model="queryParams.title"
              placeholder="Search learning journeys..."
              @search="handleSearch"
              clearable
              @clear="handleSearch"
              @keyup="handleSearch"
              class="w-80"
            />
          </div>
        </template>

        <template #statistics>
          <div class="p-4 flex items-center justify-between">
            <div class="text-sm text-muted-foreground"> Found {{ total }} journeys </div>
          </div>
        </template>

        <!-- Scrollable Content: Journey Grid -->
        <div class="flex-1 overflow-hidden">
          <div class="pb-4 px-4 h-full">
            <JourneyGrid
              :journey-list="journeyList"
              :loading="loading"
              current-tab-label="journeys"
              @journey-click="handleJourneyClick"
            />
          </div>
        </div>

        <template #footer v-if="total > 0">
          <div class="px-4 flex items-center w-full h-full">
            <SmartPagination
              class="w-full"
              :current-page="queryParams.pageNum"
              :page-size="queryParams.pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
// Remove old styles as they're now handled by modern components
</style>
