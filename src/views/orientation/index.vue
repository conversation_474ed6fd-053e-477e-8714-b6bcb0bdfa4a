<script setup lang="ts">
import {
  categoryList,
  orientationNavList,
  OrientationNavListVO,
  OrientationNavReqVO
} from '@/api/orientation'
import OrientationGrid from './components/OrientationGrid.vue'
import { Button } from '@/components/ui/button'
import { RefreshCcw, BookOpen, Folder, GraduationCap } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { SmartPagination } from '@/components/SmartPagination'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  type SidebarProps,
} from '@/components/ui/sidebar'

// Props for Sidebar
const props = defineProps<SidebarProps>()

const courseList = ref<Array<OrientationNavListVO>>([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 20,
  title: '',
  categoryId: undefined
})
const total = ref(0)
const loading = ref(false)
const categoryData = ref<{ id: number; title: string; sort: number }[]>([])
const router = useRouter()
const getCategoryList = async () => {
  categoryData.value = await categoryList('')
}
const getOrientationList = async () => {
  const params = {
    ...queryParams.value,
    pageNo: queryParams.value.pageNum // Convert pageNum to pageNo for API
  }
  delete params.pageNum // Remove pageNum to avoid confusion
  try {
    loading.value = true
    const data = await orientationNavList(params as OrientationNavReqVO)
    if (data && data.list) {
      courseList.value = data.list
      total.value = data.total
    } else {
      courseList.value = []
      total.value = 0
    }
  } finally {
    loading.value = false
  }
}
const handleSearch = () => {
  queryParams.value.pageNum = 1
  queryParams.value.pageSize = 20
  getOrientationList()
}
const handleReset = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNum = 1
  queryParams.value.categoryId = undefined
  queryParams.value.title = ''
  handleSearch()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNum = newPage
  getOrientationList()
}

// Handle orientation click
const handleOrientationClick = (orientation: OrientationNavListVO) => {
  router.push({ name: 'OrientationDetail', params: { id: orientation.id } })
}

// Handle category navigation
const handleCategoryNavigation = (categoryId: number | undefined) => {
  queryParams.value.categoryId = categoryId
  queryParams.value.pageNum = 1 // Reset to first page
  getOrientationList()
}

onMounted(() => {
  getOrientationList()
  getCategoryList()
})
</script>

<template>
  <ContainerWrapper :autoCollapse="true">
    <!-- Left Navigation -->
    <template #nav>
      <Sidebar v-bind="props" :collapsible="'none'" class="border-r-0 w-full bg-white">
        <SidebarHeader class="border-b-0">
          <div class="flex items-center gap-3 px-2 py-3">
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <GraduationCap class="size-4" />
            </div>
            <div class="flex flex-col gap-0 leading-none">
              <span class="font-semibold text-sidebar-foreground">Orientation Library</span>
              <span class="text-xs text-sidebar-foreground/60">Learning Platform</span>
            </div>
          </div>
        </SidebarHeader>

        <SidebarContent class="px-0 overflow-y-auto">
          <!-- Categories Section -->
          <SidebarGroup>
            <SidebarGroupLabel>Categories</SidebarGroupLabel>
            <SidebarGroupContent class="px-0">
              <SidebarMenu>
                <!-- All Categories -->
                <SidebarMenuItem>
                  <SidebarMenuButton
                    class="w-full"
                    @click="handleCategoryNavigation(undefined)"
                    :is-active="!queryParams.categoryId"
                  >
                    <Folder class="size-4" />
                    <span>All Categories</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <!-- Dynamic Categories from API -->
                <SidebarMenuItem v-for="category in categoryData" :key="category.id">
                  <SidebarMenuButton
                    @click="handleCategoryNavigation(category.id)"
                    :is-active="queryParams.categoryId === category.id"
                  >
                    <BookOpen class="size-4" />
                    {{ category.title }}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
    </template>

    <!-- Main Content Area -->
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="px-4 flex items-center justify-between gap-4 w-full h-full">
            <!-- Left: Search Box -->
            <SuperSearch
              v-model="queryParams.title"
              :loading="loading"
              placeholder="Search orientations..."
              @search="handleSearch"
              clearable
              @clear="handleSearch"
              @keyup="handleSearch"
            />
          </div>
        </template>
        <template #statistics>
          <div class="p-4 flex items-center justify-between">
            <div class="text-sm text-muted-foreground"> Found {{ total }} orientations </div>
          </div>
        </template>
        <!-- Scrollable Content: Orientation Grid -->
        <div class="flex-1 overflow-hidden">
          <div class="pb-4 px-4 h-full">
            <OrientationGrid
              :orientation-list="courseList"
              :loading="loading"
              current-tab-label="orientations"
              @orientation-click="handleOrientationClick"
            />
          </div>
        </div>

        <template #footer v-if="total > 0">
          <div class="px-4 flex items-center w-full h-full">
            <SmartPagination
              class="w-full"
              :current-page="queryParams.pageNum"
              :page-size="queryParams.pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss"></style>
