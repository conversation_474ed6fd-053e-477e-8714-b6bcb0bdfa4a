<script setup lang="ts" name="OrientationDetail">
import { getOrientationDetail } from '@/api/orientation'
import { useRoute, useRouter } from 'vue-router'
import { formatImgUrl } from '@/utils/tool'
import { VideoPlayer } from '@videojs-player/vue'
import 'video.js/dist/video-js.css'
import axios from 'axios'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import {
  ArrowLeft,
  Info,
  Calendar,
  User,
  FileText,
  Clock,
  Download,
  Eye,
  Play,
  Folder,
  Tag
} from 'lucide-vue-next'
import { SuperBreadcrumb } from '@/components/common'
const route = useRoute()
const router = useRouter()

// 指引列表
const orientation = ref()
const fileType = ref()
const loading = ref(true)
const showVideo = ref(false)
const resqUrl = ref()

const coursePlayData = computed(() => ({
  sources: [
    {
      src: formatImgUrl(resqUrl.value as string),
      type: 'video/mp4'
    }
  ]
}))

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (!bytes) return 'N/A'
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i]
}

// 获取文件类型标签
const getFileTypeLabel = (fileType: string) => {
  const types = {
    pdf: 'PDF Document',
    mp4: 'Video File',
    doc: 'Word Document',
    docx: 'Word Document',
    ppt: 'PowerPoint',
    pptx: 'PowerPoint'
  }
  return types[fileType?.toLowerCase()] || fileType?.toUpperCase() || 'File'
}

// 返回上一页
const goBack = () => {
  router.push('/orientation/center')
}

// 下载单个文件
const downloadSingleFile = (file: any) => {
  if (file) {
    const link = document.createElement('a')
    link.href = formatImgUrl(file.fileUrl)
    link.download = file.fileName || 'download'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 下载文件（保留兼容性）
const downloadFile = () => {
  if (orientation.value?.attachmentList?.[0]) {
    downloadSingleFile(orientation.value.attachmentList[0])
  }
}
const getCompanyPolicy = async () => {
  try {
    loading.value = true
    const data = await getOrientationDetail(Number(route.params.id))
    orientation.value = data
    if (data.attachmentList && data.attachmentList.length > 0) {
      fileType.value = data.attachmentList[0].fileType
      if (fileType.value === 'pdf' || fileType.value === 'PDF') {
        showVideo.value = false
        if (data.attachmentList[0].fileUrl.includes('https')) {
          axios
            .request({ baseURL: data.attachmentList[0].fileUrl, responseType: 'blob' })
            .then((abc) => {
              const blob = new Blob([abc.data], { type: 'application/pdf' })
              const url = window.URL.createObjectURL(blob)
              resqUrl.value = url
            })
        } else {
          resqUrl.value = formatImgUrl(data.attachmentList[0].fileUrl)
        }
      } else {
        resqUrl.value = data.attachmentList[0].fileUrl
        showVideo.value = true
      }
    }
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  getCompanyPolicy()
})
</script>
<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <!-- Header -->
        <template #header>
          <div class="flex items-center px-4 gap-3 w-full h-full">
            <SuperBreadcrumb
              current-title="Orientation Detail"
              show-back-button
            />
            <!-- Left Section: Breadcrumb -->
            <div class="flex items-center gap-3">
              <Breadcrumb />
              <Badge v-if="orientation" variant="outline" class="text-xs font-medium">
                {{ getFileTypeLabel(fileType) }}
              </Badge>
            </div>

            <!-- Center Section: Title -->
            <div class="flex-1 flex justify-center">
              <div v-if="orientation" class="text-center">
                <h1 class="text-lg font-semibold text-gray-900">{{ orientation.title }}</h1>
              </div>
            </div>
          </div>
        </template>

        <!-- Content Area -->
        <div class="p-6">
          <!-- Loading state -->
          <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
            <div class="flex flex-col items-center space-y-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span class="text-gray-600 text-lg">Loading orientation details...</span>
            </div>
          </div>

          <!-- Orientation Details -->
          <div v-else-if="orientation">
            <!-- Basic Information -->
            <Card>
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <Info class="w-5 h-5 mr-2" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Description -->
                  <div class="md:col-span-2">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Description</Label
                    >
                    <p class="mt-2 text-gray-600 leading-relaxed">
                      {{ orientation.description || 'No description available' }}
                    </p>
                  </div>

                  <!-- Category -->
                  <div v-if="orientation.categoryName">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Category</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Tag class="w-4 h-4 mr-2" />
                      <span>{{ orientation.categoryName }}</span>
                    </div>
                  </div>

                  <!-- Creator -->
                  <div v-if="orientation.creator">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Creator</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <User class="w-4 h-4 mr-2" />
                      <span>{{ orientation.creator }}</span>
                    </div>
                  </div>

                  <!-- Create Time -->
                  <div v-if="orientation.createTime">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Create Time</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Calendar class="w-4 h-4 mr-2" />
                      <span>{{ formatDateTime(orientation.createTime) }}</span>
                    </div>
                  </div>

                  <!-- Update Time -->
                  <div v-if="orientation.updateTime">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Update Time</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Clock class="w-4 h-4 mr-2" />
                      <span>{{ formatDateTime(orientation.updateTime) }}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- File Content -->
            <Card class="mt-8">
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <FileText class="w-5 h-5 mr-2" />
                  Content
                </CardTitle>
              </CardHeader>
              <CardContent>
                <!-- Video Player -->
                <div v-if="showVideo" class="mb-6">
                  <VideoPlayer
                    :seeking="false"
                    class="video-player vjs-big-play-centered rounded-lg overflow-hidden"
                    controls
                    playsinline
                    :sources="coursePlayData.sources"
                    :poster="formatImgUrl(orientation?.cover)"
                    crossorigin="anonymous"
                    :width="800"
                    :height="450"
                  />
                </div>

                <!-- PDF Viewer -->
                <div v-else-if="resqUrl" class="mb-6">
                  <iframe
                    id="mapFrame"
                    :src="resqUrl"
                    class="w-full border rounded-lg min-h-[800px]"
                    allow="fullscreen"
                  />
                </div>

                <!-- File Information -->
                <div v-if="orientation.attachmentList?.length > 0" class="mt-6">
                  <h4 class="text-lg font-medium mb-4">File Information</h4>
                  <div class="space-y-3">
                    <div
                      v-for="(file, index) in orientation.attachmentList"
                      :key="index"
                      class="bg-gray-50 rounded-lg p-4 flex items-center justify-between"
                    >
                      <div class="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span class="font-medium text-gray-700">File Name:</span>
                          <p class="text-gray-600 mt-1">{{ file.fileName }}</p>
                        </div>
                        <div>
                          <span class="font-medium text-gray-700">File Type:</span>
                          <p class="text-gray-600 mt-1">{{ getFileTypeLabel(file.fileType) }}</p>
                        </div>
                        <div>
                          <span class="font-medium text-gray-700">File Size:</span>
                          <p class="text-gray-600 mt-1">{{ formatFileSize(file.size) }}</p>
                        </div>
                      </div>
                      <div class="ml-4">
                        <Button
                          @click="downloadSingleFile(file)"
                          variant="outline"
                          size="sm"
                          class="text-xs font-medium"
                        >
                          <Download class="w-4 h-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Error state -->
          <div v-else class="flex items-center justify-center min-h-[400px]">
            <div class="text-center text-gray-500">
              <div
                class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4 mx-auto"
              >
                <FileText class="w-12 h-12 text-slate-400" />
              </div>
              <h3 class="text-lg font-semibold text-slate-900 mb-2">Content not found</h3>
              <p class="text-slate-500 max-w-md">
                The orientation content you're looking for doesn't exist or has been removed.
              </p>
            </div>
          </div>
        </div>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss"></style>
