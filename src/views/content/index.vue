<template>
  <ContainerWrapper :autoCollapse="true">
    <!-- Left Navigation -->
    <template #nav>
      <Sidebar v-bind="props" :collapsible="'none'" class="border-r-0 w-full bg-white">
        <SidebarHeader class="border-b-0">
          <div class="flex items-center gap-3 px-2 py-3">
            <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
              <GalleryVerticalEnd class="size-4" />
            </div>
            <div class="flex flex-col gap-0 leading-none">
              <span class="font-semibold text-sidebar-foreground">Course Library</span>
              <span class="text-xs text-sidebar-foreground/60">Learning Platform</span>
            </div>
          </div>
        </SidebarHeader>

        <SidebarContent class="px-0 overflow-y-auto">

          <!-- Topics Section -->
          <SidebarGroup>
            <SidebarGroupLabel>Topics</SidebarGroupLabel>
            <SidebarGroupContent class="px-0">
              <SidebarMenu>
                <!-- All Topics - Top Level Menu Item -->
                <SidebarMenuItem>
                  <SidebarMenuButton
                    class="w-full"
                    @click="handleNavigation(undefined)"
                    :is-active="!topicId"
                  >
                    <Folder class="size-4" />
                    <span>All Topics</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>

                <!-- Dynamic Topics from API as direct siblings -->
                <SidebarMenuItem v-for="topic in topLevelTopics" :key="topic.id">
                  <SidebarMenuButton
                    @click="handleNavigation(topic.id)"
                    :is-active="topicId === topic.id"
                  >
                    <!-- Use cover image if available, otherwise use folder icon for topics with children -->
                    <img v-if="topic.cover" :src="topic.cover" alt="" class="size-4 rounded mr-2" />
                    <Folder v-else-if="getChildrenSubject(topic.id).length > 0" class="size-4 mr-2" />
                    <BookOpen v-else class="size-4 mr-2" />
                    {{ topic.name }}
                  </SidebarMenuButton>

                  <!-- Child topics (if any) - as sub-items -->
                  <SidebarMenuSub v-if="getChildrenSubject(topic.id).length > 0">
                    <SidebarMenuSubItem
                      v-for="childTopic in getChildrenSubject(topic.id)"
                      :key="childTopic.id"
                    >
                      <SidebarMenuSubButton
                        @click="handleNavigation(childTopic.id)"
                        :is-active="topicId === childTopic.id"
                        class="text-xs"
                      >
                        <!-- Use cover image if available, otherwise use BookOpen for child topics -->
                        <img v-if="childTopic.cover" :src="childTopic.cover" alt="" class="size-3 rounded mr-2" />
                        <BookOpen v-else class="size-3 mr-2" />
                        {{ childTopic.name }}
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  </SidebarMenuSub>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>


      </Sidebar>
    </template>

    <!-- Main Content Area -->
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="px-4 flex items-center justify-between gap-4 w-full h-full">
            <!-- Left: Search Box -->
            <SuperSearch
              v-model="searchQuery"
              :loading="loading"
              @search="handleSearch"
              @keyup="handleSearch"
              clearable
              @clear="handleSearch"
              placeholder="Search courses..."
              class="w-80"
            />

            <!-- Right: Filter and Clear Buttons -->
            <div class="flex items-center gap-3">
              <!-- Filter Button -->
              <Button variant="outline" size="sm" @click="showFilters = !showFilters" class="h-8">
                <Filter class="size-4 mr-2" />
                Filters
                <ChevronDown
                  :class="['size-4 ml-1 transition-transform', showFilters ? 'rotate-180' : '']"
                />
              </Button>

              <!-- Clear Button -->
              <Button
                v-if="searchQuery || hasActiveFilters"
                variant="ghost"
                size="sm"
                @click="clearAllFilters"
                class="text-muted-foreground h-8"
              >
                <X class="size-3 mr-1" />
                Clear All
              </Button>
            </div>
          </div>
        </template>
        <template #filter>
          <!-- Expanded Filters -->
          <Transition
            enter-active-class="transition-all duration-200 ease-in-out"
            enter-from-class="opacity-0 -translate-y-2"
            enter-to-class="opacity-100 translate-y-0"
            leave-active-class="transition-all duration-150 ease-in-out"
            leave-from-class="opacity-100 translate-y-0"
            leave-to-class="opacity-0 -translate-y-2"
          >
            <div v-if="showFilters" class="px-4 py-3 border-b bg-white">
              <ClickableFilters :filters="currentFilters" @filter-change="handleFilterChange" />
            </div>
          </Transition>
        </template>
        <template #statistics>
          <div class="p-4 flex items-center justify-between">
            <div class="text-sm text-muted-foreground"> Found {{ totalCourses }} courses </div>
            <div class="flex items-center gap-3">
              <Label class="text-sm font-medium text-muted-foreground whitespace-nowrap"
                >Sort by:</Label
              >
              <div class="flex flex-wrap gap-1">
                <button
                  v-for="option in sortOptions"
                  :key="option.value"
                  @click="handleSortChange(option.value)"
                  :class="[
                    'inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium transition-colors border',
                    sortBy === option.value
                      ? 'bg-primary text-primary-foreground border-primary shadow-sm'
                      : 'bg-background text-muted-foreground border-border hover:bg-muted hover:text-foreground hover:border-muted-foreground'
                  ]"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>
          </div>
        </template>

        <!-- Scrollable Content: Course Grid -->
        <div class="flex-1 overflow-hidden">
          <ScrollArea class="h-full">
            <div class="pb-4 px-4 h-full">
              <CourseGrid
                :courses="courseList"
                :loading="loading"
                empty-title="No courses found"
                empty-description="Try adjusting your search or filter criteria"
                :show-clear-button="hasActiveFilters"
                @course-click="handleCourseClick"
                @clear-filters="clearAllFilters"
              />
            </div>
          </ScrollArea>
        </div>

        <template #footer v-if="total > 0">
          <div class="px-4 flex items-center w-full h-full">
            <SmartPagination
              class="w-full"
              :current-page="queryParams.pageNo"
              :page-size="queryParams.pageSize"
              :total="total"
              @page-change="handlePageChange"
            />
          </div>
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useRouteQuery } from '@vueuse/router'
import { X, Filter, ChevronDown, ChevronRight, GalleryVerticalEnd, BookOpen, Folder, Star, TrendingUp } from 'lucide-vue-next'
import { useCourseMenu } from './hooks/useCourseMenu'
import CourseNav from './components/CourseNav.vue'
import ClickableFilters from './components/ClickableFilters.vue'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { SuperSearch } from '@/components/SuperSearch'
import { SmartPagination } from '@/components/SmartPagination'
import CourseGrid from './components/CourseGrid.vue'
import {
  StudyStatusEnum,
  CourseTypeEnum,
  CourseLevelEnum,
  LanguageEnum,
  CourseSourceEnum,
  DurationTypeEnum,
  NewCourseTypeEnum,
  CourseApi,
  type CourseQueryParams,
  type CourseRespVO,
  type CourseTopicVO,
  type PageResult
} from '@/api/learning/course'

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
  SidebarSeparator,
  type SidebarProps,
} from '@/components/ui/sidebar'


// Props for Sidebar
const props = defineProps<SidebarProps>()

// Use course menu hook
const {
  menuItems,
  activeMenuItem,
  activeTopicId,
  // searchQuery,
  // loading: menuLoading,
  switchTopic,
  searchCourses,
  // clearSearch
} = useCourseMenu()

// Router and URL parameters
const router = useRouter()
const topicId = useRouteQuery('topicId', '', { transform: Number })
const searchQuery = ref('')

// Navigation handler
const handleNavigation = (topicIdValue: number | undefined) => {
  if (topicIdValue) {
    // Update URL with topicId parameter
    router.push({
      path: '/content',
      query: { topicId: topicIdValue }
    })
  } else {
    // Clear topicId parameter for "All" view
    router.push({
      path: '/content'
    })
  }
}

// Check if menu item is active
const isActiveItem = (item: any) => {
  const currentPath = router.currentRoute.value.path
  const currentQuery = router.currentRoute.value.query

  // Check if it's the current path
  if (item.url === currentPath) return true

  // Check if it matches current query parameters
  if (item.url.includes('courseType')) {
    const urlObj = new URL(item.url, window.location.origin)
    const courseTypeParam = urlObj.searchParams.get('courseType')
    return courseTypeParam && parseInt(courseTypeParam) === sortBy.value
  }

  if (item.url.includes('parentTopicId')) {
    const urlObj = new URL(item.url, window.location.origin)
    const parentTopicIdParam = urlObj.searchParams.get('parentTopicId')
    return parentTopicIdParam && parseInt(parentTopicIdParam) === queryParams.value.parentTopicId
  }

  return false
}

// Data state
const loading = ref(false)
const courseList = ref<CourseRespVO[]>([])
const total = ref(0)

// Topic/Subject related state
const topicList = ref<CourseTopicVO[]>([])
const topicLoading = ref(false)

// Subject breadcrumb state
interface SubjectItem {
  isChildren: boolean
  cover: string
  subjectId: number | undefined
  subjectTitle: string
  children: CourseTopicVO[]
}

const subjectArray = ref<SubjectItem[]>([])
const subjectItem = ref({
  cover: '',
  subjectId: undefined,
  subjectTitle: ''
})

// Computed properties for Sidebar
const topLevelTopics = computed(() => {
  return topicList.value.filter(topic => topic.parentId === 0)
})



// Real data functions

// Filter state
const showFilters = ref(false)
const currentFilters = ref({
  language: LanguageEnum.ENGLISH,
  level: CourseLevelEnum.SUITABLE_FOR_ALL,
  subtitle: '',
  source: CourseSourceEnum.LOCAL,
  duration: DurationTypeEnum.LESS_THAN_15,
  studyStatus: undefined as StudyStatusEnum | undefined,
  courseType: undefined as CourseTypeEnum | undefined
})

// Sorting and pagination state
const sortBy = ref(NewCourseTypeEnum.NEW)

// Query parameters for API calls
const queryParams = ref<CourseQueryParams>({
  pageNo: 1,
  pageSize: 12,
  name: '',
  topicId: '',
  type: undefined,
  studyStatus: undefined,
  levelList: [],
  languageList: [],
  subtitleList: [],
  sourceList: [],
  durationTypeList: []
})

// Sort options
const sortOptions = [
  { value: NewCourseTypeEnum.NEW, label: 'New Course' },
  { value: NewCourseTypeEnum.HOT, label: 'Hot Course' }
]

// Computed properties
const totalCourses = computed(() => total.value)

const hasActiveFilters = computed(() => {
  return (
    currentFilters.value.language !== LanguageEnum.ENGLISH ||
    currentFilters.value.level !== CourseLevelEnum.SUITABLE_FOR_ALL ||
    currentFilters.value.subtitle !== '' ||
    currentFilters.value.source !== CourseSourceEnum.LOCAL ||
    currentFilters.value.duration !== DurationTypeEnum.LESS_THAN_15 ||
    currentFilters.value.studyStatus !== undefined ||
    currentFilters.value.courseType !== undefined
  )
})

// Real data functions

// API Methods
const getCourseList = async () => {
  loading.value = true
  try {
    const params = {
      ...queryParams.value,
      name: searchQuery.value
    }

    // Apply filters to query params
    if (currentFilters.value.studyStatus !== undefined) {
      params.studyStatus = currentFilters.value.studyStatus
    }

    if (currentFilters.value.courseType !== undefined) {
      params.type = currentFilters.value.courseType
    }

    if (currentFilters.value.level !== CourseLevelEnum.SUITABLE_FOR_ALL) {
      params.levelList = [currentFilters.value.level]
    }

    if (currentFilters.value.language !== LanguageEnum.ENGLISH) {
      params.languageList = [currentFilters.value.language]
    }

    if (currentFilters.value.source !== CourseSourceEnum.LOCAL) {
      params.sourceList = [currentFilters.value.source]
    }

    if (currentFilters.value.duration !== DurationTypeEnum.LESS_THAN_15) {
      params.durationTypeList = [currentFilters.value.duration]
    }

    // Apply topic filter from URL parameter
    if (topicId.value) {
      params.topicId = topicId.value
    } else delete params.topicId


    // Apply courseType filter based on sort selection
    if (sortBy.value === NewCourseTypeEnum.NEW) {
      params.courseType = NewCourseTypeEnum.NEW
    } else if (sortBy.value === NewCourseTypeEnum.HOT) {
      params.courseType = NewCourseTypeEnum.HOT
    }

    console.log('API params:', params)

    try {
      const res = await CourseApi.getCourseList(params)
      courseList.value = res.list || []
      total.value = res.total || 0
    } catch (apiError) {
      console.error('Failed to fetch courses:', apiError)
      courseList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('Failed to fetch courses:', error)
    courseList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// Get all course topics
const getAllTopics = async () => {
  topicLoading.value = true
  try {
    const res = await CourseApi.getAllTopics()
    topicList.value = res || []

    // Initialize subject breadcrumb with "All" option
    if (topicList.value.length > 0) {
      subjectArray.value = [
        {
          isChildren: false,
          cover: '',
          subjectId: undefined,
          subjectTitle: 'All',
          children: topicList.value.filter((item) => item.parentId === 0)
        }
      ]
    }
  } catch (error) {
    console.error('Failed to fetch topics:', error)
    topicList.value = []
  } finally {
    topicLoading.value = false
  }
}

// Subject navigation functions (from original code)
const changeSubject = (item: CourseTopicVO, index: number) => {
  // Set query parameter
  queryParams.value.parentTopicId = item.id

  // Reset subject item if clicking top level
  if (item.parentId === 0 && subjectItem.value) {
    subjectItem.value = {
      cover: '',
      subjectId: undefined,
      subjectTitle: ''
    }
  }

  // Reset breadcrumb if clicking top level and array has multiple items
  if (item.parentId === 0 && subjectArray.value.length >= 2) {
    subjectArray.value = [
      {
        isChildren: false,
        cover: '',
        subjectId: undefined,
        subjectTitle: 'All',
        children: topicList.value.filter((topic) => topic.parentId === 0)
      }
    ]
  }

  // Clear subsequent breadcrumb items
  if (index !== 0) {
    subjectArray.value.splice(index + 1)
    subjectItem.value = {
      cover: '',
      subjectId: undefined,
      subjectTitle: ''
    }
  }

  // Get children topics
  const children = topicList.value.filter((topic) => topic.parentId === item.id)
  if (children.length > 0) {
    // Avoid duplicates
    const existingSubject = subjectArray.value.find((subject) => subject.subjectId === item.id)
    if (!existingSubject) {
      subjectArray.value.push({
        isChildren: true,
        cover: item.cover,
        subjectId: item.id,
        subjectTitle: item.name,
        children: children
      })
    }
  } else if (subjectArray.value.length >= 1) {
    // No children, set as final selection
    subjectItem.value.subjectId = item.id
    subjectItem.value.subjectTitle = item.name
    subjectItem.value.cover = item.cover
  }

  getCourseList()
}

const changeTopSubject = (id: number | undefined) => {
  // Reset to "All" if id is undefined
  if (id === undefined) {
    subjectItem.value = {
      cover: '',
      subjectId: undefined,
      subjectTitle: ''
    }
    subjectArray.value = [
      {
        isChildren: false,
        cover: '',
        subjectId: undefined,
        subjectTitle: 'All',
        children: topicList.value.filter((item) => item.parentId === 0)
      }
    ]
  }

  // Handle multi-level navigation
  if (id && getChildrenSubject(id).length > 0) {
    const index = subjectArray.value.findIndex((item) => item.subjectId === id)
    subjectArray.value.splice(index + 1)
    subjectItem.value = {
      cover: '',
      subjectId: undefined,
      subjectTitle: ''
    }
  }

  queryParams.value.parentTopicId = id
  getCourseList()
}

const getChildrenSubject = (id: number) => {
  return topicList.value.filter((topic) => topic.parentId === id)
}

// Event handlers
const handleSearch = () => {
  queryParams.value.pageNo = 1 // Reset to first page when searching
  queryParams.value.name = searchQuery.value
  getCourseList()
}

const handleSortChange = (value: NewCourseTypeEnum | undefined) => {
  if (value !== undefined) {
    sortBy.value = value
    queryParams.value.pageNo = 1 // Reset to first page when sorting
    getCourseList()
  }
}

const handlePageChange = (page: number) => {
  queryParams.value.pageNo = page
  getCourseList()
}

const handleCourseClick = (course: any) => {
  console.log('Course clicked:', course)
  // Navigate to course detail page
  if (course && course.id) {
    router.push({
      path: `/content/detail/${course.id}`
    })
  }
}

// Filter change handler
const handleFilterChange = (filters: any) => {
  currentFilters.value = { ...filters }
  queryParams.value.pageNo = 1 // Reset to first page when filtering
  getCourseList()
}

// Clear all filters
const clearAllFilters = () => {
  searchQuery.value = ''
  currentFilters.value = {
    language: LanguageEnum.ENGLISH,
    level: CourseLevelEnum.SUITABLE_FOR_ALL,
    subtitle: '',
    source: CourseSourceEnum.LOCAL,
    duration: DurationTypeEnum.LESS_THAN_15,
    studyStatus: undefined,
    courseType: undefined
  }
  queryParams.value.pageNo = 1
  getCourseList()
}

// Lifecycle hooks
onMounted(async () => {
  // Load topics first, then courses
  await getAllTopics()
  getCourseList()
})

onActivated(() => {
  getCourseList()
})
</script>
