<script setup lang="ts">
import Catalogue from './components/Catalogue/index.vue'
import Appendix from './components/Appendix/index.vue'
import ScormPlay from './components/ScormPlay/index.vue'
import VideoPlay from './components/VideoPlay/index.vue'
import DocumentPlay from './components/DocumentPlay/index.vue'
import { CourseChapterType } from '@/enums/chapter'
import { getChapters, getCourse } from '@/api/course/details'
import { getPlay } from '@/api/course/play'
import type { CourseChapter, CourseDetail } from '@/api/course/details'
import { CourseApi, type AppCourseRespVO } from '@/api/learning/course'
import NotView from '@/assets/images/content/detail/NotView.png'
import { getConfigKey } from '@/api/infra/config'
import { Rate } from '@/components/Rate'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Home } from 'lucide-vue-next'
const loading = ref(true)
const route = useRoute()
const courseDetail = ref<AppCourseRespVO>()
const chapters = ref<CourseChapter[]>()
const playDetail = ref<CourseChapter>()

// 课程ID和章节ID
const courseId = computed(() => Number(route.params.id))
const chapterId = computed(() => Number(route.params.chapterId))

// Tab配置
const activeTab = ref('catalogue')

// 文件预览配置
const filePreviewConfig = ref<number>()

async function getDetail() {
  try {
    loading.value = true

    // 并行获取数据
    const [courseRes, chaptersRes, playRes] = await Promise.all([
      CourseApi.getCourse(courseId.value),
      getChapters(courseId.value),
      getPlay(chapterId.value)
    ])

    // 处理API响应数据
    courseDetail.value = courseRes || courseRes
    chapters.value = chaptersRes || chaptersRes
    playDetail.value = playRes || playRes

    // 增加页面访问量
    if (courseDetail.value?.id) {
      CourseApi.addPageView(courseDetail.value.id)
    }
  } catch (error) {
    console.error('=== Error in getDetail ===', error)
  } finally {
    loading.value = false
  }
}

async function getPreviewConfig() {
  try {
    const config = await getConfigKey('file.preview.config')
    filePreviewConfig.value = Number(config)
  } catch (error) {
    console.error('Failed to load preview config:', error)
  }
}
onMounted(async () => {
  await Promise.all([getPreviewConfig(), getDetail()])
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <template #header>
          <div class="flex items-center px-4 gap-3 w-full h-full">
            <!-- Breadcrumb Navigation -->
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/" class="flex items-center gap-1">
                    <Home class="w-4 h-4" />
                    Home
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/my-center">Course Library</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink :href="`/content/detail/${courseId}`"
                    >Course Detail</BreadcrumbLink
                  >
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Play</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </template>

        <div class="p-6">
          <!-- Always show this for now to debug -->
          <div class="mb-4 p-4 bg-blue-50 rounded">
            <h2 class="text-lg font-bold mb-2">Course Player</h2>
            <div class="text-sm space-y-1">
              <div>Loading: {{ loading }}</div>
              <div>Course ID: {{ courseId }}</div>
              <div>Chapter ID: {{ chapterId }}</div>
              <div>PlayDetail exists: {{ !!playDetail }}</div>
              <div>CourseDetail exists: {{ !!courseDetail }}</div>
              <div v-if="playDetail">PlayDetail.isRecord: {{ playDetail.isRecord }}</div>
              <div v-if="playDetail">PlayDetail.type: {{ playDetail.type }}</div>
            </div>
          </div>

          <div v-if="loading" class="min-h-[400px] flex items-center justify-center">
            <div class="text-center">
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"
              ></div>
              <span class="text-gray-600 text-sm">Loading course content...</span>
            </div>
          </div>

          <div v-else-if="!playDetail" class="min-h-[400px] flex items-center justify-center">
            <div class="text-center">
              <LazyImage :src="NotView" class="mx-auto mb-4" />
              <span class="text-gray-600 text-sm">No course content found</span>
            </div>
          </div>

          <div v-else-if="playDetail && playDetail.isRecord" class="flex gap-6 h-full">
            <!-- Left: Player Area -->
            <div class="flex-1 min-w-0">
              <div class="bg-black rounded-lg overflow-hidden aspect-video">
                <template v-if="playDetail">
                  <!-- Document Course -->
                  <DocumentPlay
                    v-if="playDetail.type === CourseChapterType.Document"
                    :data="playDetail"
                    :course-id="courseId"
                    :chapter-id="chapterId"
                  />
                  <!-- Video/Audio Course -->
                  <VideoPlay
                    v-else-if="
                      [CourseChapterType.Video, CourseChapterType.Audio].includes(playDetail.type)
                    "
                    :data="playDetail"
                    :course-data="courseDetail"
                    :course-id="courseId"
                    :chapter-id="chapterId"
                  />
                  <!-- SCORM Course -->
                  <ScormPlay
                    v-else
                    :data="playDetail"
                    :file-preview-config="filePreviewConfig"
                    :course-data="courseDetail"
                    :course-id="courseId"
                    :chapter-id="chapterId"
                  />
                </template>
              </div>
            </div>

            <!-- Right: Course Info & Navigation -->
            <div class="w-80 flex flex-col bg-white rounded-lg border">
              <!-- Course Header -->
              <div class="p-4 border-b">
                <div class="flex gap-3">
                  <img
                    :src="courseDetail?.cover || '/placeholder-course.jpg'"
                    :alt="courseDetail?.name"
                    class="w-16 h-16 rounded object-cover flex-shrink-0"
                  />
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-sm line-clamp-2 mb-1" :title="courseDetail?.name">
                      {{ courseDetail?.name }}
                    </h3>
                    <Rate
                      v-if="courseDetail?.star"
                      :default-value="courseDetail.star"
                      readonly
                      :size="14"
                    />
                  </div>
                </div>
              </div>

              <!-- Tabs -->
              <Tabs v-model="activeTab" class="flex-1 flex flex-col overflow-hidden">
                <TabsList class="m-4 mb-2">
                  <TabsTrigger value="catalogue" class="flex-1"> Catalogue </TabsTrigger>
                  <TabsTrigger value="appendix" class="flex-1"> Appendix </TabsTrigger>
                </TabsList>

                <TabsContent value="catalogue" class="flex-1 overflow-hidden mt-0">
                  <Catalogue
                    :play-detail="playDetail"
                    :data="chapters"
                    :course-id="courseId"
                    :chapter-id="chapterId"
                  />
                </TabsContent>

                <TabsContent value="appendix" class="flex-1 overflow-hidden mt-0">
                  <Appendix
                    :play-detail="playDetail"
                    :data="chapters"
                    :course-id="courseId"
                    :chapter-id="chapterId"
                  />
                </TabsContent>
              </Tabs>
            </div>
          </div>

          <div v-else class="min-h-[400px] flex items-center justify-center">
            <div class="text-center">
              <LazyImage :src="NotView" class="mx-auto mb-4" />
              <span class="text-gray-600 text-sm">
                The course can't be played, please enter through the Course Catalogue.
              </span>
            </div>
          </div>
        </div>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
// 简洁的播放页面样式
</style>
