<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useMessage } from '@/hooks/web/useMessage'
import { Star, Award, Users, Home } from 'lucide-vue-next'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { SuperBreadcrumb } from '@/components/common/SuperBreadcrumb'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { Rate } from '@/components/Rate'
import Catalogue from './components/Catalogue.vue'
import SatisfactionSurvey from './components/SatisfactionSurvey.vue'
import NotAdd from './components/NotAdd.vue'
import NotCert from './components/NotCert.vue'
import Certificate from './components/Certificate.vue'
import {
  CourseApi,
  type AppCourseRespVO,
  CourseTypeEnum,
  StudyStatusEnum,
  formatCourseDuration,
  getCourseTypeLabel,
  getStudyStatusLabel
} from '@/api/learning/course'
import { ChatBaseTypeEnum, useChatBaseStore } from '@/store/modules/chatBase'

const route = useRoute()
const message = useMessage()
const chatBaseStore = useChatBaseStore()

// 响应式数据
const courseDetail = ref<AppCourseRespVO>()
const chapters = ref<any[]>([])
const loading = ref(true)
const courseId = Number(route.params.id)

// 验证courseId是否有效
if (!courseId || isNaN(courseId)) {
  console.error('Invalid course ID:', route.params.id)
}

// 计算属性
const isReadOnly = computed(() => courseDetail.value?.type !== null)
const defaultTab = computed(() => {
  if (route.query.showCertificate === '1') return 'certificate'
  return 'catalogue'
})

// 课程状态相关
const courseStatusBadgeClass = computed(() => {
  if (!courseDetail.value) return ''
  switch (courseDetail.value.studyStatus) {
    case StudyStatusEnum.PENDING:
      return 'bg-gray-100 text-gray-800'
    case StudyStatusEnum.IN_PROGRESS:
      return 'bg-blue-100 text-blue-800'
    case StudyStatusEnum.COMPLETED:
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
})

/**
 * 获取课程详细信息
 */
const getCourseDetail = async () => {
  try {
    loading.value = true

    // 获取课程详情
    const courseData = await CourseApi.getCourse(courseId)
    courseDetail.value = courseData

    // 获取课程章节
    const chaptersData = await CourseApi.getCourseChapters(courseId)
    chapters.value = chaptersData

    // 驱动AI助手的课程更新提示
    if (courseDetail.value) {
      chatBaseStore.updateNewChatBiz({
        type: ChatBaseTypeEnum.COURSE,
        typeId: courseId,
        title: courseDetail.value.name,
        cover: courseDetail.value.cover,
        selected: false
      })
    }
  } catch (error) {
    console.error('Failed to fetch course detail:', error)
    message.error('Failed to load course information')
  } finally {
    loading.value = false
  }
}

/**
 * 处理添加/移除课程
 */
const handleAddCourse = async () => {
  if (!courseDetail.value) return

  const isOperation = await message.confirm(
    isReadOnly.value ? 'Sure to remove the course?' : 'Sure to add the course?'
  )

  if (isOperation) {
    try {
      loading.value = true

      if (isReadOnly.value) {
        await CourseApi.removeFromMyCourse(courseId)
        message.success('Course removed successfully')
      } else {
        await CourseApi.addToMyCourse({ courseId })
        message.success('Course added successfully')
      }

      // 重新获取课程详情以更新状态
      await getCourseDetail()
    } catch (error) {
      console.error('Failed to update course:', error)
      message.error('Operation failed')
    } finally {
      loading.value = false
    }
  }
}

onMounted(() => {
  getCourseDetail()
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <Tabs :default-value="defaultTab" class="h-full flex flex-col overflow-hidden">
        <ContainerScroll>
          <template #header>
            <div class="flex items-center justify-between px-4 w-full h-full">
              <!-- Left Section: Breadcrumb and Status -->
                <SuperBreadcrumb
                  current-title="Course Detail"
                  :show-back-button="true"
                />

              <!-- Right Section: Action Buttons -->
              <div class="flex items-center gap-2">
                <Button
                  v-if="courseDetail && [null, CourseTypeEnum.ELECTIVE].includes(courseDetail.type)"
                  :variant="isReadOnly ? 'outline' : 'default'"
                  size="sm"
                  @click="handleAddCourse"
                  :disabled="loading"
                  class="text-xs font-medium"
                >
                  {{ isReadOnly ? 'Remove Course' : 'Add to My Course' }}
                </Button>
              </div>
            </div>
          </template>

          <!-- Course Basic Information -->
          <div class="p-6 bg-white border-b">
            <div class="flex space-x-5">
              <!-- Course Cover -->
              <img
                :src="courseDetail?.cover || '/placeholder-course.jpg'"
                :alt="courseDetail?.name"
                class="h-[180px] min-w-[160px] rounded object-cover"
              />

              <!-- Course Details -->
              <div class="flex-1 overflow-hidden">
                <div class="flex items-center">
                  <span class="text-lg font-bold text-gray-900">{{ courseDetail?.name }}</span>
                  <div
                    v-if="courseDetail?.type === CourseTypeEnum.MANDATORY"
                    class="ms-5 w-[100px] h-[30px] bg-[#DBE2F9] rounded-[4px] text-sm flex items-center justify-center"
                  >
                    Mandatory
                  </div>
                </div>
                <div class="flex items-center gap-3">
                  <!-- Rating -->
                  <Rate
                    v-if="courseDetail?.star"
                    :default-value="courseDetail.star"
                    readonly
                    class="me-auto"
                  />
                  <Badge
                    v-if="courseDetail?.studyStatus !== undefined"
                    :class="courseStatusBadgeClass"
                    class="text-xs font-medium"
                  >
                    {{ getStudyStatusLabel(courseDetail.studyStatus) }}
                  </Badge>
                  <Badge
                    v-if="courseDetail?.type === CourseTypeEnum.MANDATORY"
                    class="bg-blue-100 text-blue-800 text-xs font-medium"
                  >
                    {{ getCourseTypeLabel(courseDetail.type) }}
                  </Badge>
                </div>

                <!-- Course Introduction -->
                <div class="mt-4">
                  <ScrollArea class="max-h-[120px] overflow-y-auto w-auto">
                    <div class="text-muted-foreground">
                      {{ courseDetail?.introduction || 'No introduction available.' }}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </div>
          </div>

          <!-- Tabs Content Area -->
          <div class="p-6">
            <TabsList class="mb-6">
              <TabsTrigger value="catalogue">
                Course Catalogue
              </TabsTrigger>
              <TabsTrigger value="survey">
                Satisfaction Survey
              </TabsTrigger>
              <TabsTrigger value="certificate">
                Certificate
              </TabsTrigger>
            </TabsList>

            <TabsContent value="catalogue" class="mt-0">
              <Catalogue v-if="isReadOnly" :data="chapters" />
              <NotAdd v-else @add="handleAddCourse" />
            </TabsContent>

            <TabsContent value="survey" class="mt-0">
              <SatisfactionSurvey
                v-if="isReadOnly"
                :course-id="courseId"
                :course-data="courseDetail"
              />
              <NotAdd v-else @add="handleAddCourse" />
            </TabsContent>

            <TabsContent value="certificate" class="mt-0">
              <Certificate
                v-if="courseDetail?.isCertificateGenerated && courseDetail?.certificateUrl"
                :certificate="courseDetail.certificateUrl"
                :title-name="courseDetail.name"
              />
              <NotCert v-else />
            </TabsContent>
          </div>
        </ContainerScroll>
      </Tabs>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
// 简洁的课程详情页面样式
</style>
