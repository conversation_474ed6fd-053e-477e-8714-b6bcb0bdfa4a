<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import { ArrowLeft, Clock, CheckCircle, AlertCircle, Eye, FileText } from 'lucide-vue-next'
import QuestionViewRenderer from '../components/QuestionViewRenderer.vue'
import { SurveyApi } from '@/api/survey'
import type { SurveyResponseDetailVO } from '@/api/survey/types'
import { formatToDateTime } from '@/utils/dateUtil'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'

const route = useRoute()
const router = useRouter()

// Reactive data
const loading = ref(true)
const responseDetail = ref<SurveyResponseDetailVO | null>(null)

// Computed properties
const responseId = computed(() => Number(route.params.responseId))

// Methods
const fetchResponseDetail = async () => {
  loading.value = true
  try {
    console.log('Fetching response detail for responseId:', responseId.value)
    const response = await SurveyApi.getResponseDetail(responseId.value)
    console.log('Response detail API response:', response)

    if (response && response.data) {
      responseDetail.value = response.data
      console.log('Response detail loaded successfully:', response.data)
    } else if (response) {
      responseDetail.value = response
      console.log('Response detail loaded (no data field):', response)
    } else {
      console.warn('Response is empty or null')
      responseDetail.value = null
    }
  } catch (error) {
    console.error('Failed to fetch response detail:', error)
    responseDetail.value = null
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  // Return to survey detail page
  if (responseDetail.value?.instanceId) {
    router.push(`/survey/detail/${responseDetail.value.instanceId}`)
  } else {
    // If no responseDetail, try to return from referrer or history
    if (document.referrer && document.referrer.includes('/survey/detail/')) {
      window.history.back()
    } else {
      router.push('/my-center/index?tab=surveys')
    }
  }
}

// Lifecycle
onMounted(() => {
  fetchResponseDetail()
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll :header-height="100">
        <!-- Header: Top information bar -->
        <template #header>
          <div class="flex items-center px-4 gap-3 w-full h-full">
            <!-- Left Section: Breadcrumb and Status -->
            <div class="flex items-center gap-3">
              <Breadcrumb />
              <Badge variant="outline" class="text-xs font-medium">
                <Eye class="w-3 h-3 mr-1" />
                Read-only Mode
              </Badge>
            </div>

            <!-- Center Section: Survey Title -->
            <div class="flex-1 flex justify-center">
              <div v-if="responseDetail" class="text-center">
                <h1 class="text-lg font-semibold text-gray-900">{{ responseDetail.instanceName }}</h1>
                <div class="text-xs text-gray-600 mt-1">
                  Submit Time: {{ responseDetail.submitTime ? formatToDateTime(responseDetail.submitTime) : 'Unknown' }}
                </div>
              </div>
              <div v-else class="text-center">
                <h1 class="text-lg font-semibold text-gray-900">View Response Record</h1>
              </div>
            </div>

            <!-- Right Section: Action Buttons -->
            <div class="flex items-center gap-2">
              <!-- Back Button -->
              <div class="flex items-center gap-1">
                <Tooltip>
                  <TooltipTrigger as-child>
                    <Button variant="ghost" size="icon" @click="goBack" class="h-8 w-8">
                      <ArrowLeft class="w-4 h-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Back to Survey Detail</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>
        </template>

        <!-- Loading state -->
        <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
          <div class="flex flex-col items-center space-y-4">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span class="text-gray-600 text-lg">Loading response record...</span>
          </div>
        </div>

        <!-- Main content area: Questions and answers -->
        <div v-else-if="responseDetail" class="bg-gray-50 p-6">
          <!-- Show all questions when there is question data -->
          <div v-if="responseDetail.answers && responseDetail.answers.length > 0" class="space-y-6">
            <!-- Iterate through all questions -->
            <Card
              v-for="(answer, index) in responseDetail.answers"
              :key="answer.id || index"
              class="mb-6"
            >
              <CardHeader>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <Badge variant="secondary" class="text-xs"> Question {{ index + 1 }}</Badge>
                    <Badge variant="outline" class="text-xs">
                      {{ answer.questionTypeName || 'Unknown Type' }}
                    </Badge>
                  </div>
                  <div v-if="answer.required" class="text-red-500 text-sm"> * Required</div>
                </div>
                <CardTitle class="text-lg leading-relaxed">
                  {{ answer.title || 'Unknown Question' }}
                </CardTitle>
                <CardDescription v-if="answer.description">
                  {{ answer.description }}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <!-- Question renderer -->
                <QuestionViewRenderer :question="answer" :answer="answer" :readonly="true" />
              </CardContent>
            </Card>
          </div>

          <!-- No questions state -->
          <div v-else class="text-center py-12">
            <FileText class="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">No question data</h3>
            <p class="text-gray-600">No question data found in this response record.</p>
          </div>
        </div>

        <!-- Error state -->
        <div v-else class="flex items-center justify-center min-h-[400px]">
          <div class="text-center max-w-md mx-auto px-4">
            <AlertCircle class="w-16 h-16 text-red-500 mx-auto mb-6" />
            <h1 class="text-2xl font-bold text-gray-900 mb-4">Record not found</h1>
            <p class="text-gray-600 mb-8"
              >The response record you are looking for does not exist or has been deleted.</p
            >
          </div>
        </div>


      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss">
.survey-view-container {
  .survey-view-content {
    min-height: 100vh;
  }
}
</style>
