<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Learning Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #2c6fbb;
            --secondary: #3a9dff;
            --accent: #ff6b6b;
            --light: #f0f7ff;
            --dark: #1a3a5f;
            --success: #4caf50;
            --warning: #ff9800;
            --info: #2196f3;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #e6f2ff 0%, #f0f8ff 100%);
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        header {
            background: linear-gradient(to right, var(--primary), var(--secondary));
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header-content {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .profile {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #64b5f6, #bbdefb);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            margin-right: 20px;
            border: 3px solid white;
        }
        
        .user-info {
            text-align: left;
        }
        
        .user-info h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }
        
        .user-info p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .report-period {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 10px 20px;
            display: inline-block;
            margin: 15px 0;
            font-size: 18px;
        }
        
        .summary {
            font-size: 18px;
            max-width: 700px;
            margin: 20px auto 0;
            line-height: 1.6;
            background: rgba(255, 255, 255, 0.15);
            padding: 15px;
            border-radius: 10px;
        }
        
        .section-title {
            font-size: 24px;
            color: var(--primary);
            margin: 30px 0 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--secondary);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            background: var(--secondary);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 0 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e0e7ff;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: var(--light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: var(--primary);
            font-size: 20px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .card-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary);
            margin: 10px 0;
        }
        
        .card-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .progress-container {
            background: #f0f4ff;
            border-radius: 10px;
            height: 10px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(to right, var(--secondary), var(--primary));
        }
        
        .chart-container {
            height: 250px;
            position: relative;
            margin: 20px 0;
        }
        
        .skill-chart-container {
            height: 600px;
            position: relative;
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 25px;
            padding: 0 30px;
            margin-bottom: 30px;
        }
        
        .analysis-box {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #e0e7ff;
        }
        
        .full-width-analysis {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #e0e7ff;
            margin: 0 30px 30px;
            width: calc(100% - 60px);
        }
        
        .tag {
            display: inline-block;
            background: var(--light);
            color: var(--primary);
            padding: 5px 15px;
            border-radius: 20px;
            margin: 5px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .recommendation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            padding: 25px;
            margin: 0 30px 30px;
        }
        
        .rec-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }
        
        .rec-title {
            color: var(--primary);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .rec-title i {
            margin-right: 10px;
            color: var(--success);
        }
        
        .course-list {
            list-style-type: none;
            padding-left: 20px;
        }
        
        .course-list li {
            padding: 8px 0;
            border-bottom: 1px dashed #e0e0e0;
            display: flex;
            align-items: center;
        }
        
        .course-list li:last-child {
            border-bottom: none;
        }
        
        .course-list li i {
            color: var(--success);
            margin-right: 10px;
        }
        
        footer {
            background: var(--dark);
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
        }
        
        .improvement {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #fff8e1;
            border-radius: 10px;
        }
        
        .improvement i {
            color: var(--warning);
            margin-right: 10px;
            font-size: 20px;
        }
        
        .skill-bar {
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 5px 0 15px;
            overflow: hidden;
        }
        
        .skill-level {
            height: 100%;
            border-radius: 4px;
            background: var(--primary);
        }
        
        .highlight {
            background: linear-gradient(120deg, #e0f7fa 0%, #bbdefb 100%);
            border-left: 4px solid var(--primary);
            padding: 15px;
            border-radius: 0 10px 10px 0;
            margin: 20px 0;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .metric-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary);
            margin: 5px 0;
        }
        
        .metric-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .skill-tag {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 3px;
            font-size: 12px;
        }
        
        .weakness-item, .interest-item {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
        }
        
        .weakness-item {
            background: #ffebee;
        }
        
        .interest-item {
            background: #e8f5e9;
        }
        
        .weakness-title, .interest-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .metric-row {
            display: flex;
            gap: 10px;
            margin-top: 8px;
            flex-wrap: wrap;
        }
        
        .metric-item {
            background: rgba(255, 255, 255, 0.5);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 13px;
            display: inline-flex;
            align-items: center;
        }
        
        .metric-box {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            flex: 1;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            min-width: 120px;
        }
        
        .metric-box.on-time {
            background-color: #e8f5e9;
            border: 2px solid #4caf50;
        }
        
        .metric-box.delayed {
            background-color: #ffebee;
            border: 2px solid #f44336;
        }
        
        .metric-box-title {
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 16px;
            color: var(--dark);
        }
        
        .metric-box-value {
            font-size: 28px;
            font-weight: 700;
            margin: 10px 0;
        }
        
        .metric-box.on-time .metric-box-value {
            color: #4caf50;
        }
        
        .metric-box.delayed .metric-box-value {
            color: #f44336;
        }
        
        .metric-box-description {
            font-size: 13px;
            margin-top: 5px;
            color: #666;
        }
        
        .legend-container {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 14px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            margin-right: 8px;
        }
        
        .legend-color.on-time {
            background-color: #4caf50;
        }
        
        .legend-color.delayed {
            background-color: #f44336;
        }
        
        .chart-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--primary);
        }
        
        .skill-analysis-box {
            grid-column: 1 / -1;
        }
        
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
                padding: 0 15px;
            }
            
            .cards-container {
                padding: 0 15px;
            }
            
            .header-content {
                padding: 0 15px;
            }
            
            .recommendation {
                margin: 0 15px 30px;
            }
            
            .profile {
                flex-direction: column;
                text-align: center;
            }
            
            .avatar {
                margin-right: 0;
                margin-bottom: 15px;
            }
            
            .metric-box {
                margin: 10px 0;
            }
            
            .legend-container {
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
            
            .chart-container {
                height: 220px;
            }
            
            .skill-chart-container {
                height: 400px;
            }
            
            .full-width-analysis {
                margin: 0 15px 30px;
                width: calc(100% - 30px);
            }
        }
        
        .skill-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .skill-highlight {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            padding: 10px 15px;
            font-size: 16px;
        }
        
        .skill-highlight strong {
            color: var(--primary);
        }
        
        /* TNI System Styles */
        .tni-pagination {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 10px;
        }
        
        .tni-page-btn {
            padding: 5px 12px;
            background: var(--light);
            border: 1px solid var(--secondary);
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tni-page-btn:hover {
            background: var(--secondary);
            color: white;
        }
        
        .tni-page-btn.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        
        .tni-content {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .tni-skill-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--light);
        }
        
        .radar-chart-container {
            height: 600px;
            position: relative;
            margin: 20px 0;
            width: 100%;
        }
        
        .requirements-container {
            height: auto;
            min-height: 300px;
        }
        
        /* New filter styles */
        .category-filter {
            margin: 15px 0;
            padding: 10px 15px;
            border-radius: 8px;
            border: 1px solid var(--secondary);
            background-color: var(--light);
            font-size: 16px;
            width: 250px;
        }
        
        .filter-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .filter-container label {
            font-weight: 600;
            color: var(--dark);
        }
        
        /* Added for the pie chart */
        .pie-chart-container {
            height: 300px;
            position: relative;
            margin: 20px auto;
            max-width: 500px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="profile">
                    <div class="avatar">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                    <div class="user-info">
                        <h1>Zhang Ming | Data Analyst</h1>
                        <p>ID: DA2023057 | Dept: Data Science Center</p>
                    </div>
                </div>
                
                <div class="report-period">
                    <i class="fas fa-calendar-alt"></i> Report Period: June 11, 2024 - June 11, 2025 (1 year)
                </div>
                
                <div class="summary">
                    <i class="fas fa-lightbulb"></i> 
                    Learning Profile: <strong>"Active Explorer, Strong Technical Skills, Management Basics Need Improvement"</strong>
                </div>
            </div>
        </header>
        
        <main>
            <div class="section-title">
                <i class="fas fa-chart-line"></i>
                <h2>Core Learning Metrics</h2>
            </div>
            
            <div class="cards-container">
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-title">Learning Engagement</div>
                    </div>
                    <div class="card-value">328 hrs</div>
                    <div class="card-description">Total learning hours</div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: 85%"></div>
                    </div>
                    <div class="card-description">Active days: 286 days (Avg 1.15 hrs/day)</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="card-title">Learning Progress</div>
                    </div>
                    <div class="card-value">36</div>
                    <div class="card-description">Completed courses</div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: 78%"></div>
                    </div>
                    <div class="card-description">Completion rate: 78%</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <div class="card-title">Skill Coverage</div>
                    </div>
                    <div class="card-value">68%</div>
                    <div class="card-description">Job skills mastered</div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: 68%"></div>
                    </div>
                    <div class="card-description">5 out of 8 core skills mastered</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <div class="card-title">Learning Effectiveness</div>
                    </div>
                    <div class="card-value">82%</div>
                    <div class="card-description">Average exam score</div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: 82%"></div>
                    </div>
                    <div class="card-description">Certificates: 4</div>
                </div>
            </div>
            
            <div class="section-title">
                <i class="fas fa-chart-bar"></i>
                <h2>Learning Progress Analysis</h2>
            </div>
            
            <!-- Learning Requirements Completion - Full Width -->
            <div class="full-width-analysis requirements-container">
                <h3><i class="fas fa-book"></i> Learning Requirements Completion</h3>
                <div class="highlight">
                    <p><strong>Required Skills:</strong> Python, SQL, Data Visualization, Machine Learning, Statistics, Data Cleaning, Reporting, Project Management</p>
                </div>
                
                <div class="improvement">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>Certificates Earned:</strong> Python Data Analysis, Advanced SQL, Data Visualization Expert, Tableau Certification
                    </div>
                </div>
                
                <div class="improvement">
                    <i class="fas fa-check-circle"></i>
                    <div>
                        <strong>Courses Completed:</strong> 28 out of 36 courses (78% completion)
                    </div>
                </div>
                
                <div class="improvement" style="background: #fff3e0;">
                    <div>
                        <i class="fas fa-times-circle"></i> <strong>Courses Skipped:</strong> Skipped 8 learning contents of 3 skills<br><br>
                        <i class="fas fa-info-circle"></i> <strong>Skip Analysis:</strong> Content perceived as too advanced, lack of immediate project relevance, and overlap with existing knowledge.
                    </div>
                </div>
            </div>
            
            <!-- Learning content completion rate (by skill) - Full Width -->
            <div class="full-width-analysis">
                <h3><i class="fas fa-brain"></i> Learning content completion rate (by skill)</h3>
                <div class="filter-container">
                    <label for="categoryFilter">Filter by category:</label>
                    <select id="categoryFilter" class="category-filter">
                        <option value="all">All categories</option>
                        <option value="hard" selected>Hard skill</option>
                        <option value="soft">Soft skill</option>
                        <option value="hse">HSE skill</option>
                    </select>
                </div>
                <div class="radar-chart-container">
                    <canvas id="skillsChart"></canvas>
                </div>
                <div class="skill-highlight">
                    <i class="fas fa-star"></i> <strong>Top Skill:</strong> Data Visualization (95%) 
                    | <i class="fas fa-exclamation-triangle"></i> <strong>Needs Improvement:</strong> Project Management (60%)
                </div>
            </div>
            
            <div class="grid-2">
                <div class="analysis-box">
                    <h3><i class="fas fa-exclamation-triangle"></i> Identify Skill Weaknesses</h3>
                    
                    <div class="weakness-item">
                        <div class="weakness-title">Machine Learning</div>
                        <div class="metric-row">
                            <div class="metric-item">
                                <i class="fas fa-bug"></i> Error Rate: 42%
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-check-circle"></i> Completion Rate: 65%
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-book-open"></i> Course Access: 18
                            </div>
                            <!-- Removed Planned metric -->
                        </div>
                    </div>
                    
                    <div class="weakness-item">
                        <div class="weakness-title">Project Management</div>
                        <div class="metric-row">
                            <div class="metric-item">
                                <i class="fas fa-bug"></i> Error Rate: 38%
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-check-circle"></i> Completion Rate: 58%
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-book-open"></i> Course Access: 12
                            </div>
                            <!-- Removed Planned metric -->
                        </div>
                    </div>
                    
                    <h4 style="margin-top: 20px;">Weakness Distribution Analysis</h4>
                    <div class="chart-container">
                        <canvas id="weaknessChart"></canvas>
                    </div>
                </div>
                
                <div class="analysis-box">
                    <h3><i class="fas fa-heart"></i> Discover Learning Interests</h3>
                    
                    <div class="interest-item">
                        <div class="interest-title">Data Visualization</div>
                        <div class="metric-row">
                            <div class="metric-item">
                                <i class="fas fa-star"></i> Rating: 4.8
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-book"></i> TNI Requests: False
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-check-circle"></i> Completion Rate: 95%
                            </div>
                            <!-- Removed Hours metric -->
                        </div>
                    </div>
                    
                    <div class="interest-item">
                        <div class="interest-title">AI Fundamentals</div>
                        <div class="metric-row">
                            <div class="metric-item">
                                <i class="fas fa-star"></i> Rating: 4.5
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-book"></i> TNI Requests: True
                            </div>
                            <div class="metric-item">
                                <i class="fas fa-check-circle"></i> Completion Rate: 85%
                            </div>
                            <!-- Removed Hours metric -->
                        </div>
                    </div>
                    
                    <h4 style="margin-top: 20px;">Top Rated Skills</h4>
                    <div class="chart-container">
                        <canvas id="ratingChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="section-title">
                <i class="fas fa-clipboard-list"></i>
                <h2>Learning Method Analysis</h2>
            </div>
            
            <div class="grid-2">
                <!-- Removed Learning Plan Compliance section -->
                
                <div class="analysis-box">
                    <h3><i class="fas fa-clock"></i> Learning Time Analysis</h3>
                    <div class="pie-chart-container">
                        <canvas id="timeChart"></canvas>
                    </div>
                    
                    <div class="improvement" style="background: #fff3e0; margin-top: 20px;">
                        <i class="fas fa-moon"></i>
                        <div>
                            <strong>Late-Night Learning:</strong> 70% of learning concentrated between 22:00-02:00, recommend adjusting schedule
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section-title">
                <i class="fas fa-lightbulb"></i>
                <h2>Learning Improvement Recommendations</h2>
            </div>
            
            <div class="recommendation">
                <!-- Removed TNI System Approved Courses section -->
                
                <div class="rec-card">
                    <h3 class="rec-title"><i class="fas fa-user-graduate"></i> Learning Method Optimization</h3>
                    <ul class="course-list">
                        <li><i class="fas fa-check"></i> Time Management: Create daily learning schedule, avoid late-night sessions</li>
                        <li><i class="fas fa-check"></i> Deep Learning: Reduce video skip rate (current 35% → target 15%)</li>
                        <li><i class="fas fa-check"></i> Practice Focus: Complete 2 data analysis projects weekly</li>
                        <li><i class="fas fa-check"></i> Regular Review: Weekly review of notes and mistakes</li>
                    </ul>
                </div>
            </div>
        </main>
        
        <footer>
            <p>© 2025 Enterprise Learning Platform - Annual Learning Report</p>
            <p>Generated: June 11, 2025 | Data Version: v2.5.1</p>
        </footer>
    </div>

    <script>
        // Generate skill names for different categories
        function generateSkillNamesForCategory(category) {
            const skillCategories = [
                "Data Analysis", "Machine Learning", "Data Engineering", 
                "Data Visualization", "Cloud Computing", "Database", 
                "Programming", "Statistics", "Business Intelligence", 
                "DevOps", "Big Data", "AI"
            ];
            
            const skillLevels = ["Basic", "Intermediate", "Advanced", "Expert"];
            const skillTypes = ["Techniques", "Methods", "Systems", "Frameworks", "Tools"];
            
            let count;
            switch (category) {
                case 'hard':
                    count = 15;
                    break;
                case 'soft':
                    count = 20;
                    break;
                case 'hse':
                    count = 10;
                    break;
                default:
                    count = 60;
            }
            
            const skills = [];
            for (let i = 0; i < count; i++) {
                const categoryIndex = Math.floor(Math.random() * skillCategories.length);
                const levelIndex = Math.floor(Math.random() * skillLevels.length);
                const typeIndex = Math.floor(Math.random() * skillTypes.length);
                
                skills.push(`${skillCategories[categoryIndex]} ${skillLevels[levelIndex]} ${skillTypes[typeIndex]}`);
            }
            return skills;
        }
        
        // Generate random data for the radar chart based on category
        function generateRadarDataForCategory(count) {
            const data = [];
            for (let i = 0; i < count; i++) {
                // Generate values between 20 and 95
                data.push(Math.floor(Math.random() * 76) + 20);
            }
            return data;
        }
        
        let myChart;
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Radar Chart (skillsChart)
            const skillsCtx = document.getElementById('skillsChart').getContext('2d');
            const initialCategory = 'hard';
            const skillNames = generateSkillNamesForCategory(initialCategory);
            const skillData = generateRadarDataForCategory(15);
            myChart = new Chart(skillsCtx, {
                type: 'radar',
                data: {
                    labels: skillNames,
                    datasets: [{
                        label: 'Learning content completion rate %',
                        data: skillData,
                        fill: true,
                        backgroundColor: 'rgba(58, 157, 255, 0.2)',
                        borderColor: 'rgb(58, 157, 255)',
                        pointBackgroundColor: 'rgb(58, 157, 255)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(58, 157, 255)',
                        pointRadius: 3
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100,
                            ticks: {
                                stepSize: 20
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                font: {
                                    size: 12
                                }
                            }
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
            // Category filter for radar chart
            const categoryFilter = document.getElementById('categoryFilter');
            categoryFilter.addEventListener('change', function() {
                const selectedCategory = this.value;
                let count;
                switch (selectedCategory) {
                    case 'hard':
                        count = 15;
                        break;
                    case 'soft':
                        count = 20;
                        break;
                    case 'hse':
                        count = 10;
                        break;
                    default:
                        count = 60;
                }
                const newSkillNames = generateSkillNamesForCategory(selectedCategory);
                const newSkillData = generateRadarDataForCategory(count);
                myChart.destroy();
                const skillsCtx = document.getElementById('skillsChart').getContext('2d');
                myChart = new Chart(skillsCtx, {
                    type: 'radar',
                    data: {
                        labels: newSkillNames,
                        datasets: [{
                            label: 'Learning content completion rate %',
                            data: newSkillData,
                            fill: true,
                            backgroundColor: 'rgba(58, 157, 255, 0.2)',
                            borderColor: 'rgb(58, 157, 255)',
                            pointBackgroundColor: 'rgb(58, 157, 255)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgb(58, 157, 255)',
                            pointRadius: 3
                        }]
                    },
                    options: {
                        maintainAspectRatio: false,
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 100,
                                ticks: {
                                    stepSize: 20
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            title: {
                                display: false
                            }
                        }
                    }
                });
            });
            // Weakness Chart (bar)
            const weaknessCtx = document.getElementById('weaknessChart').getContext('2d');
            // 虚拟数据：error rate最高的前五个技能
            const weaknessSkills = ['Deep Learning', 'Project Management', 'Data Mining', 'Big Data', 'Cloud Computing'];
            const weaknessErrorRates = [56, 48, 45, 42, 38];
            new Chart(weaknessCtx, {
                type: 'bar',
                data: {
                    labels: weaknessSkills,
                    datasets: [{
                        label: 'Error Rate (%)',
                        data: weaknessErrorRates,
                        backgroundColor: [
                            '#ff6b6b'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                        title: {
                            display: true,
                            text: 'Weakness Distribution Analysis'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 60,
                            title: {
                                display: true,
                                text: 'Error Rate (%)'
                            }
                        }
                    }
                }
            });
            
            // Rating Chart (bar)
            const ratingCtx = document.getElementById('ratingChart').getContext('2d');
            // 虚拟数据：评分最高的前五个技能
            const ratingSkills = ['Data Visualization', 'AI Fundamentals', 'Python Programming', 'Data Engineering', 'Machine Learning'];
            const ratingValues = [4.8, 4.7, 4.6, 4.5, 4.4];
            new Chart(ratingCtx, {
                type: 'bar',
                data: {
                    labels: ratingSkills,
                    datasets: [{
                        label: 'Rating (out of 5)',
                        data: ratingValues,
                        backgroundColor: [
                            '#4caf50'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                        title: {
                            display: true,
                            text: 'Top Rated Skills'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5,
                            title: {
                                display: true,
                                text: 'Rating (out of 5)'
                            }
                        }
                    }
                }
            });
            
            // Time Chart (pie)
            const timeCtx = document.getElementById('timeChart').getContext('2d');
            new Chart(timeCtx, {
                type: 'pie',
                data: {
                    labels: ['00-06', '07-12', '13-18', '19-23'],
                    datasets: [{
                        label: 'Learning Time Distribution',
                        data: [5, 15, 10, 70],
                        backgroundColor: [
                            '#3a9dff', '#4caf50', '#ffd166', '#ff6b6b'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Learning Time Distribution by Period'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>