<template>
  <div v-loading="contentLoading">
    <div>
      <h3 class="text-lg font-medium"> Basic Info </h3>
      <p class="text-sm text-muted-foreground"> Basic information about live broadcast </p>
    </div>
    <Separator />
    <!-- Edit Mode Layout -->
    <div v-if="isEditMode" class="mt-4 space-y-4">
      <!-- Cover -->
      <FormField name="cover">
        <FormItem>
          <FormLabel>Cover</FormLabel>
          <div class="w-[333px] overflow-hidden">
            <AspectRatio :ratio="16 / 9" class="relative overflow-hidden rounded-md bg-muted">
              <!-- 图片 -->
              <img
                :src="liveBasicForm.cover"
                alt="Cover"
                class="object-cover w-full h-full cursor-pointer"
              />

              <!-- hover 显示遮罩 (仅编辑模式) -->
              <div
                v-if="isEditMode"
                class="absolute inset-0 flex items-center justify-center transition-opacity duration-300 bg-black bg-opacity-50 opacity-0 hover:opacity-100"
              >
                <div class="flex space-x-4" v-show="!uploading">
                  <Button variant="ghost" class="text-white hover:bg-white hover:text-gray-800">
                    <EyeIcon class="w-8 h-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    class="text-white hover:bg-white hover:text-gray-800"
                    @click="triggerFileUpload"
                  >
                    <UploadIcon class="w-8 h-8" />
                  </Button>
                  <Button
                    variant="ghost"
                    class="text-white hover:bg-white hover:text-gray-800"
                    @click="switchCover"
                  >
                    <RefreshCcwIcon class="w-8 h-8" />
                  </Button>
                </div>
                <input
                  id="fileInput"
                  type="file"
                  :accept="'.jpg,.jpeg,.png'"
                  class="hidden"
                  @change="handleFileUpload"
                />
              </div>
              <!-- 上传组件 (进度显示在图片中央) (仅编辑模式) -->
              <template v-if="isEditMode">
                <SuperUpload
                  v-for="fileInfo in fileInfoList"
                  :key="fileInfo"
                  :file-info="fileInfo"
                  @prepared="handlePrepared"
                  @complete="handleComplete"
                  @error="handleError"
                >
                  <template #default="{ progress }">
                    <!-- 上传进度遮罩 -->
                    <div
                      v-if="progress.percentage > 0 && progress.percentage < 100"
                      class="absolute inset-0 flex items-center justify-center overflow-hidden bg-black bg-opacity-60"
                    >
                      <div class="flex flex-col items-center">
                        <CircularProgress
                          :progress="Number(progress.percentage.toFixed(0))"
                        ></CircularProgress>
                      </div>
                    </div>
                  </template>
                </SuperUpload>
              </template>
            </AspectRatio>
          </div>
        </FormItem>
      </FormField>
      <!-- Time -->
      <FormField name="time">
        <FormItem>
          <FormLabel>Time</FormLabel>
          <FormControl v-if="isEditMode">
            <div class="flex">
              <div class="flex-row items-center justify-between flex-1 gap-2 space-x-2">
                <DatePicker ref="startDateRef" @change="handleStartDateChange"></DatePicker>
                <TimeSelect
                  ref="startTimeRef"
                  start="00:00"
                  end="23:45"
                  :step="15"
                  @change="handleStartTimeChange"
                ></TimeSelect>
                <span class="text-stone-400">-</span>
                <DatePicker ref="endDateRef" @change="handleEndDateChange"></DatePicker>
                <TimeSelect
                  ref="endTimeRef"
                  start="00:00"
                  end="23:45"
                  :step="15"
                  @change="handleEndTimeChange"
                ></TimeSelect>
              </div>
            </div>
          </FormControl>
        </FormItem>
      </FormField>
      <!-- Live broadcast title -->
      <FormField name="name">
        <FormItem>
          <FormLabel>Live broadcast title</FormLabel>
          <FormControl v-if="isEditMode">
            <Input
              type="text"
              placeholder="Live broadcast title"
              v-model="liveBasicForm.name"
              maxlength="100"
            />
          </FormControl>
          <FormDescription v-if="isEditMode">
            This is the live broadcast display title, but don't exceed 100 characters.
          </FormDescription>
        </FormItem>
      </FormField>

      <!-- Overview -->
      <FormField name="description">
        <FormItem>
          <FormLabel>Overview</FormLabel>
          <FormControl v-if="isEditMode">
            <Textarea
              placeholder="Live broadcast description"
              v-model="liveBasicForm.description"
              maxlength="2000"
            />
          </FormControl>
          <FormDescription v-if="isEditMode">
            Add a description for the live broadcast, but don't exceed 2000 characters.
          </FormDescription>
        </FormItem>
      </FormField>
    </div>

    <!-- View Mode Layout -->
    <div v-else class="mt-4">
      <!-- Cover -->
      <div class="mb-6">
        <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Cover</Label>
        <div class="mt-2 w-[333px] overflow-hidden">
          <AspectRatio :ratio="16 / 9" class="relative overflow-hidden rounded-md bg-muted">
            <img :src="liveBasicForm.cover" alt="Cover" class="object-cover w-full h-full" />
          </AspectRatio>
        </div>
      </div>

      <!-- Information Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Live broadcast title -->
        <div class="md:col-span-2">
          <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
            >Live broadcast title</Label
          >
          <div class="mt-2 text-sm text-gray-900 font-medium">
            {{ liveBasicForm.name }}
          </div>
        </div>

        <!-- Time -->
        <div>
          <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide">Time</Label>
          <div class="mt-2 flex items-center text-sm text-gray-600">
            <Calendar class="w-4 h-4 mr-2" />
            {{ formatTimestampToDateAndTime(Number(liveBasicForm.startTime)) }} -
            {{ formatTimestampToDateAndTime(Number(liveBasicForm.endTime)) }}
          </div>
        </div>

        <!-- Overview -->
        <div class="md:col-span-2">
          <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
            >Overview</Label
          >
          <div class="mt-2 flex items-start text-sm text-gray-600">
            <FileText class="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
            <div class="flex-1">
              <div
                v-if="liveBasicForm.description"
                class="text-gray-900 whitespace-pre-wrap leading-relaxed"
              >
                {{ liveBasicForm.description }}
              </div>
              <div v-else class="text-gray-500 italic">No description provided</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Label } from '@/components/ui/label'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import {
  RoomCreateOrUpdateOrPublishReqVO,
  DeptAndUserMixedVO,
  RoomDetailVO,
  RoomUserVO,
  RoomAttachmentVO,
  LiveApi
} from '@/api/live/stream'
import cover1 from '@/assets/live/management/default_cover_1.jpg'
import cover2 from '@/assets/live/management/default_cover_2.jpg'
import cover3 from '@/assets/live/management/default_cover_3.jpg'
import DatePicker from './DatePicker.vue'
import TimeSelect from './TimeSelect.vue'
import { useUserStore } from '@/store/modules/user'
import {
  getOneHourFifteenMinutesLater,
  get15Minutes,
  getTodayDate,
  isFutureDate,
  isTimeBefore,
  isTimeBeforeCurrent,
  formatLiveDate2,
  formatTimestamp,
  formatTime,
  compareTime,
  formatTimestampToDateAndTime,
  formatLiveDate,
  formatTimestampToHourMinute,
  toCalendarDate,
  getTomorrowDate
} from '@/utils/formatTime'
import dayjs from 'dayjs'
import UserPickerForm from './UserPicker.vue'
import { SuperUpload } from '@/components/SuperUpload'
import { FileInfo } from '@/components/SuperUpload/src/config'
import { checkFile } from '@/utils/fileUtil'
import { nanoid } from 'nanoid'
import { UploadResult } from '@/components/SuperUpload/src/config'
import { type DateValue, getLocalTimeZone, CalendarDate } from '@internationalized/date'
import {
  Eye as EyeIcon,
  Upload as UploadIcon,
  RefreshCcw as RefreshCcwIcon,
  Calendar,
  FileText
} from 'lucide-vue-next'
import { nextTick } from 'vue'

interface DepartmentAndUserData extends DeptAndUserMixedVO {
  isChecked: boolean
}
interface Options {
  key: number
  label: string
}
interface LiveOptions {
  label: string
  value: string
}
const liveOptions = ref<LiveOptions[]>([
  { label: 'Pending', value: '10' },
  { label: 'Upcoming', value: '20' },
  { label: 'Living', value: '50' }
])
defineOptions({
  name: 'LiveBasicInfo'
})
const props = defineProps({
  roomDetail: {
    type: Object,
    default: () => ({})
  },
  isEditMode: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['next', 'save'])

const userStore = useUserStore()
const currentUser = computed(() => userStore.user)
const speakers = ref<DepartmentAndUserData[]>([])
const visible = ref(false)
const message = useMessage()
const startDate = ref()
const endDate = ref()
const startDateRef = ref()
const endDateRef = ref()
const startTimeRef = ref()
const endTimeRef = ref()
// 找一个startDate的复制品，保存之前的值

const startDateCopy = ref()
const endDateCopy = ref()
// 开始时间，具体到分钟
const startTime = ref()
const startTimeCopy = ref()
const endTime = ref()
const endTimeCopy = ref()
const userPickerFormRef = ref()
const currentCoverIndex = ref(0)
const loading = ref(false)
// 我只想取RoomCreateOrUpdateOrPublishReqVO的如下类型
// 我只想取RoomCreateOrUpdateOrPublishReqVO的如下类型
const liveBasicForm = reactive<RoomCreateOrUpdateOrPublishReqVO>({
  name: '',
  startTime: '',
  endTime: '',
  privacy: 0,
  cover: '',
  speakerIds: [],
  description: '',
  participantIds: [],
  attachmentIds: [],
  configOptions: []
})
const coverLoading = ref(false)
const { query } = useRoute()
const id = query.id
const uploadFile = async (file: File) => {
  try {
    // 检测文件
    const checkResult = await checkFile(file, fileTypes)
    // 检测文件大小
    if (file.size > 10 * 1024 * 1024) {
      message.notifyWarning('The file size exceeds the 100MB')
      return false
    }
    // 检查通过，创建上传文件信息
    if (checkResult.can) {
      const fileInfo: FileInfo = {
        uid: nanoid(10),
        file: file,
        type: checkResult.type,
        folderId: 0, // 例如目录id，默认根目录
        relativePath: '' // 例如相对路径，默认无相对路径
      }
      // 添加到上传文件列表
      fileInfoList.value.push(fileInfo)
    }
  } catch {
  } finally {
  }
}
// 处理文件上传
const handleFileUpload = (event) => {
  const file = event.target.files[0]
  uploadFile(file)
}

// 触发文件上传
const triggerFileUpload = () => {
  const fileInput = document.getElementById('fileInput')
  if (fileInput) {
    fileInput.click()
  }
}
// 将DateValue类型的时间转为正常的Date类型
const handleCalenderToNormal = (date: DateValue) => {
  return date.toDate(getLocalTimeZone())
}
// 判断两个日期是否在同一天
const isSameDate = (start: Date, end: Date) => {
  return dayjs(start).isSame(dayjs(end), 'day')
}
const handleStartDateChange = (date: DateValue) => {
  if (!startDateRef.value || !endDateRef.value) return

  const curCheckedStartDate = handleCalenderToNormal(date)
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  // 判断开始时间是否晚于当前时间
  const isRightDate = isFutureDate(curCheckedStartDate)
  if (!isRightDate) {
    // 不可小于当前时间
    message.warning('Start time cannot be earlier than current time')
    startDateRef.value.date = startDate.value
    return
  }
  // 选择的开始时间晚于结束时间，自动将结束时间设置为开始时间
  if (dayjs(curCheckedStartDate).isAfter(dayjs(curEndDate))) {
    endDateRef.value.date = date
  } else {
    startDate.value = date
  }
}

const handleEndDateChange = (date: DateValue) => {
  if (!startDateRef.value || !endDateRef.value) return

  // 选择的结束时间早于当日，报错
  const curCheckedEndDate = handleCalenderToNormal(date)
  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const isRightDate = isFutureDate(curCheckedEndDate)
  if (!isRightDate) {
    // 不可小于当前时间
    message.warning('End time cannot be earlier than current time')
    endDateRef.value.date = endDate.value
    return
  }
  const formateStartDate = formatLiveDate2(curStartDate)
  const realStartDate = `${formateStartDate} ${startTime.value}:00`
  const formateEndDate = formatLiveDate2(curCheckedEndDate)
  const realEndDate = `${formateEndDate} ${endTime.value}:00`
  // 选择的结束时间早于开始时间（忽略时间部分），给出提示
  if (dayjs(realEndDate).isBefore(dayjs(realStartDate))) {
    message.warning('End time cannot be earlier than start time')
    // 恢复为之前的值
    endDateRef.value.date = endDate.value
    return
  } else {
    endDate.value = date
  }
}

const addOneHourToTime = (time: string): string => {
  // 将 HH:mm 格式的时间字符串转换为 Date 对象
  const [hours, minutes] = time.split(':').map(Number)
  const date = new Date()
  date.setHours(hours, minutes, 0, 0) // 设置当前时间为给定的时间
  // 增加一个小时
  date.setHours(date.getHours() + 1)

  // 格式化回 HH:mm 格式
  const newHours = String(date.getHours()).padStart(2, '0') // 保证小时是两位数
  const newMinutes = String(date.getMinutes()).padStart(2, '0') // 保证分钟是两位数

  return `${newHours}:${newMinutes}`
}
const handleStartTimeChange = (value: string) => {
  if (!startDateRef.value || !endDateRef.value || !startTimeRef.value || !endTimeRef.value) return

  // 判断value是否小于当前的时间
  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const isBefore = isTimeBeforeCurrent(value, curStartDate)
  if (isBefore) {
    message.warning('Start time cannot be earlier than current time')
    startTimeRef.value.selectedTime = startTimeCopy.value
    return
  }
  // 更新startTime的副本
  startTimeCopy.value = value
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  // 判断startDate和endDate是否为同一天，如果是，进行时间比较
  const isSameDay = isSameDate(curStartDate, curEndDate)
  if (isSameDay) {
    // 如果startTime晚于endTime，endTime需要加一个小时
    if (!compareTime(value, endTimeCopy.value)) {
      // endTime需要加一个小时
      const endTimeAddOneHour = addOneHourToTime(startTimeCopy.value)
      endTimeRef.value.selectedTime = endTimeAddOneHour
      // 如果加了一小时之后变成了以00开头的数据，那么需要重新设置endDateRef.value.date的值
      if (endTimeAddOneHour.startsWith('00')) {
        endDateRef.value.date = toCalendarDate(getTomorrowDate())
        endDate.value = toCalendarDate(getTomorrowDate())
      }
    }
  }
}

const handleEndTimeChange = (value: string) => {
  const curStartDate = startDate.value.toDate(getLocalTimeZone())
  const curEndDate = endDate.value.toDate(getLocalTimeZone())
  // 判断startDate和endDate是否为同一天，如果是，进行时间比较
  const isSameDay = isSameDate(curStartDate, curEndDate)
  if (isSameDay) {
    // 判断value是否小于当前的时间和分钟
    const isBefore = isTimeBefore(value, startTimeCopy.value)
    if (isBefore) {
      message.warning('End time cannot be earlier than start time')
      endTimeRef.value.selectedTime = endTimeCopy.value
      return
    } else {
      endTimeCopy.value = value
    }
  }
}
/**
 * 图片上传相关
 */
const switchCover = () => {
  // 睡500秒然后再执行
  currentCoverIndex.value = currentCoverIndex.value + 1
  // 除3取余，如果为0那么liveBasicForm.cover是cover1,1的话为cover2,2的话为cover3
  const index = currentCoverIndex.value % 3
  if (index == 0) {
    liveBasicForm.cover = cover1
  }
  if (index == 1) {
    liveBasicForm.cover = cover2
  }
  if (index == 2) {
    liveBasicForm.cover = cover3
  }
}

const fileInfoList = ref<FileInfo[]>([])
// 限制的文件格式
const fileTypes = ['JPG', 'JPEG', 'PNG']
const uploading = ref(false)
const handlePrepared = (value: UploadResult) => {
  uploading.value = true
}
const handleError = (errorMsg: string) => {
  console.log('errorMsg===>', errorMsg)
  uploading.value = false
}
const handleComplete = (uploadResult: UploadResult) => {
  liveBasicForm.cover = uploadResult.url
  uploading.value = false
}

// 用computed来计算两个startTime和endTime之间的时间差，注意单位是小时
const duration = computed(() => {
  const curStartDate = startDate.value?.toDate(getLocalTimeZone())
  const curEndDate = endDate.value?.toDate(getLocalTimeZone())
  const formattedStartDate = formatLiveDate2(curStartDate)
  const formattedEndDate = formatLiveDate2(curEndDate)
  // 确保时间格式正确，使用 dayjs 解析拼接的日期和时间
  const realStartDate = dayjs(`${formattedStartDate} ${startTime.value}:00`, 'YYYY-MM-DD HH:mm:ss')
  const realEndDate = dayjs(`${formattedEndDate} ${endTime.value}:00`, 'YYYY-MM-DD HH:mm:ss')
  // 计算两个日期之间相差的小时数
  const diffInHours = realEndDate.diff(realStartDate, 'hour', true)

  return diffInHours.toFixed(1) // 保留整数部分
})
const formatDate3 = (date: Date | string | null) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD')
}
const contentLoading = ref(false)
// 下一步操作
const handleSave = async () => {
  if (!liveBasicForm.name) {
    message.notifyWarning('Please input live title first')
    return
  }
  if (liveBasicForm.name.length < 2) {
    // live title长度为2-50
    message.notifyWarning('Live title must be between 2 and 50 characters')
    return
  }
  if (!startDateRef.value || !endDateRef.value) return

  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  const formattedStartDate = formatDate3(curStartDate)
  const formattedEndDate = formatDate3(curEndDate)
  const realStartDate = `${formattedStartDate} ${startTime.value}:00`
  const realEndDate = `${formattedEndDate} ${endTime.value}:00`
  liveBasicForm.startTime = String(formatTime(realStartDate))
  liveBasicForm.endTime = String(formatTime(realEndDate))
  try {
    contentLoading.value = true
    await LiveApi.updateRoom({
      id: id as unknown as string,
      ...liveBasicForm
    })

    emit('save', liveBasicForm)
  } catch {
  } finally {
    contentLoading.value = false
  }
}
// watch(
//   () => startDate.value,
//   (newValue, oldValue) => {
//     console.log('newValue===>', newValue)
//     const curStartDate = newValue.toDate(getLocalTimeZone())
//     const curEndDate = endDate.value.toDate(getLocalTimeZone())
//     const realStartDate = `${curStartDate} ${startTime.value}:00`
//     const realEndDate = `${curEndDate} ${endTime.value}:00`
//     liveBasicForm.startTime = String(formatTime(realStartDate))
//     liveBasicForm.endTime = String(formatTime(realEndDate))
//   }
// )
// watch(
//   () => endDate.value,
//   (newValue, oldValue) => {
//     const curEndDate = newValue.toDate(getLocalTimeZone())
//     const curStartDate = startDate.value.toDate(getLocalTimeZone())
//     const realStartDate = `${curStartDate} ${startTime.value}:00`
//     const realEndDate = `${curEndDate} ${endTime.value}:00`
//     liveBasicForm.startTime = String(formatTime(realStartDate))
//     liveBasicForm.endTime = String(formatTime(realEndDate))
//   }
// )
watch(
  () => props.roomDetail,
  (newData: RoomDetailVO, newValue: RoomDetailVO) => {
    if (!newData) return
    liveBasicForm.name = newData.name
    liveBasicForm.description = newData.description
    liveBasicForm.cover = newData.cover
    liveBasicForm.startTime = newData.startTime
    liveBasicForm.endTime = newData.endTime
    liveBasicForm.attachmentIds =
      newData.roomAttachments?.map((item: RoomAttachmentVO) => Number(item.fileId)) || []
    liveBasicForm.participantIds = newData.invitedUsers?.map((item: RoomUserVO) => item.userId)
    liveBasicForm.configOptions = newData.roomConfigOptions
    liveBasicForm.privacy = newData.privacy
    liveBasicForm.speakerIds = newData.speakers?.map((item: RoomUserVO) => item.userId)

    // 设置日期和时间
    startDate.value = toCalendarDate(formatTimestamp(liveBasicForm.startTime))
    endDate.value = toCalendarDate(formatTimestamp(liveBasicForm.endTime))
    startTime.value = formatTimestampToHourMinute(liveBasicForm.startTime)
    startTimeCopy.value = startTime.value
    endTime.value = formatTimestampToHourMinute(liveBasicForm.endTime)
    endTimeCopy.value = endTime.value

    // 使用 nextTick 确保 DOM 更新后再设置 ref 值
    nextTick(() => {
      if (startDateRef.value) {
        startDateRef.value.date = startDate.value
      }
      if (endDateRef.value) {
        endDateRef.value.date = endDate.value
      }
      if (startTimeRef.value) {
        startTimeRef.value.selectedTime = startTime.value
      }
      if (endTimeRef.value) {
        endTimeRef.value.selectedTime = endTime.value
      }
    })
  },
  { immediate: true }
)
// 获取表单数据的方法
const getFormData = () => {
  if (
    !startDateRef.value ||
    !endDateRef.value ||
    !startDateRef.value.date ||
    !endDateRef.value.date
  ) {
    // 如果日期选择器没有值，使用当前表单中的时间戳
    return {
      ...liveBasicForm
    }
  }

  const curStartDate = handleCalenderToNormal(startDateRef.value.date)
  const curEndDate = handleCalenderToNormal(endDateRef.value.date)
  const formattedStartDate = formatDate3(curStartDate)
  const formattedEndDate = formatDate3(curEndDate)
  const realStartDate = `${formattedStartDate} ${startTime.value}:00`
  const realEndDate = `${formattedEndDate} ${endTime.value}:00`

  return {
    ...liveBasicForm,
    startTime: String(formatTime(realStartDate)),
    endTime: String(formatTime(realEndDate))
  }
}

defineExpose({
  liveBasicForm,
  startDate,
  endDate,
  getFormData
})
onMounted(async () => {
  // 获取URL参数中的表单数据
  const route = useRoute()
  if (route.query.form) {
    try {
      const formData = JSON.parse(route.query.form as string)

      // 填充表单数据
      liveBasicForm.name = formData.name || ''
      liveBasicForm.startTime = formData.startTime || ''
      liveBasicForm.endTime = formData.endTime || ''
      liveBasicForm.cover = formData.cover || ''

      // 设置日期和时间
      if (liveBasicForm.startTime) {
        startDate.value = toCalendarDate(formatTimestamp(liveBasicForm.startTime))
        startDateCopy.value = startDate.value
        startTime.value = formatTimestampToHourMinute(liveBasicForm.startTime)
        startTimeCopy.value = startTime.value

        if (startDateRef.value) {
          startDateRef.value.date = startDate.value
        }
        if (startTimeRef.value) {
          startTimeRef.value.selectedTime = startTime.value
        }
      }

      if (liveBasicForm.endTime) {
        endDate.value = toCalendarDate(formatTimestamp(liveBasicForm.endTime))
        endDateCopy.value = endDate.value
        endTime.value = formatTimestampToHourMinute(liveBasicForm.endTime)
        endTimeCopy.value = endTime.value

        if (endDateRef.value) {
          endDateRef.value.date = endDate.value
        }
        if (endTimeRef.value) {
          endTimeRef.value.selectedTime = endTime.value
        }
      }
    } catch (error) {
      console.error('Failed to parse form data', error)
    }
  }
})
</script>

<style lang="scss" scoped></style>
