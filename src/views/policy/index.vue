<script setup lang="ts">
import { companyPolicyList, CompanyPolicyListVO, CompanyPolicyNavReqVO } from '@/api/company'
import PolicyGrid from './components/PolicyGrid.vue'
import { Button } from '@/components/ui/button'
import { RefreshCcw } from 'lucide-vue-next'
import { SuperSearch } from '@/components/SuperSearch'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { SmartPagination } from '@/components/SmartPagination'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

const courseList = ref<Array<CompanyPolicyListVO>>([])
const queryParams = ref<CompanyPolicyNavReqVO>({
  pageNo: 1,
  pageSize: 20,
  title: '',
  status: undefined
})
const total = ref(0)
const loading = ref(false)
const router = useRouter()

// Policy status options for filtering
const statusOptions = [
  { value: undefined, label: 'All Policies' },
  { value: '0', label: 'Not Started' },
  { value: '1', label: 'In Progress' },
  { value: '3', label: 'Completed' }
]

const getDataList = async () => {
  const params = {
    ...queryParams.value
  }
  try {
    loading.value = true
    const data = await companyPolicyList(params as CompanyPolicyNavReqVO)
    if (data && data.list) {
      courseList.value = data.list
      total.value = data.total
    } else {
      courseList.value = []
      total.value = 0
    }
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  queryParams.value.pageNo = 1
  queryParams.value.pageSize = 20
  getDataList()
}

const handleClear = () => {
  queryParams.value.pageSize = 20
  queryParams.value.pageNo = 1
  queryParams.value.title = ''
  queryParams.value.status = undefined
  handleSearch()
}

const handleCurrentChange = (newPage: number) => {
  queryParams.value.pageNo = newPage
  getDataList()
}

// Handle status change
const handleStatusChange = (status: string | undefined) => {
  queryParams.value.status = status
  queryParams.value.pageNo = 1 // Reset to first page
  getDataList()
}

// Handle policy click
const handlePolicyClick = (policy: CompanyPolicyListVO) => {
  router.push({
    name: 'CompanyPolicyDetail',
    params: { id: policy.id },
    query: { ack: policy.ack }
  })
}

onMounted(() => {
  getDataList()
})
</script>

<template>
  <!-- Company Policy 查询表单 -->
  <ContainerWrapper class="flex space-x-5">
    <template #content>
      <ContainerScroll>
        <template #header>
            <!-- Filter Section -->
            <div class="px-4 flex items-center justify-between gap-4 w-full h-full">
              <!-- Search Input -->
              <SuperSearch
                v-model="queryParams.title"
                :loading="loading"
                @search="handleSearch"
                @keyup="handleSearch"
                clearable
                @clear="handleSearch"
                placeholder="Search policies..."
              />

              <!-- Status Select -->
<!--              <Select v-model="queryParams.status" @update:model-value="handleStatusChange">-->
<!--                <SelectTrigger class="w-[200px]">-->
<!--                  <SelectValue placeholder="All Policies" />-->
<!--                </SelectTrigger>-->
<!--                <SelectContent>-->
<!--                  <SelectItem-->
<!--                    v-for="option in statusOptions"-->
<!--                    :key="option.value || 'all'"-->
<!--                    :value="option.value"-->
<!--                  >-->
<!--                    {{ option.label }}-->
<!--                  </SelectItem>-->
<!--                </SelectContent>-->
<!--              </Select>-->
            </div>
        </template>

        <template #statistics>
          <div class="p-4 flex items-center justify-between">
            <div class="text-sm text-muted-foreground"> Found {{ total }} orientations </div>
          </div>
        </template>

        <!-- Policy Grid -->
        <div class="pb-4 px-4 ">
          <PolicyGrid
            :policy-list="courseList"
            :loading="loading"
            current-tab-label="policies"
            @policy-click="handlePolicyClick"
          />
        </div>

        <template #footer v-if="total > 0">
          <div class="px-4 flex items-center w-full h-full">
            <SmartPagination
              class="w-full"
              :current-page="queryParams.pageNo"
              :page-size="queryParams.pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </template>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss"></style>
