<script setup lang="ts" name="CompanyPolicyDetail">
import axios from 'axios'
import { editStatus, getDetail, searchStatus } from '@/api/company'
import { useRoute, useRouter } from 'vue-router'
import { formatImgUrl } from '@/utils/tool'
import { VideoPlayer } from '@videojs-player/vue'
import 'video.js/dist/video-js.css'
import { useCounter } from '@/utils/useCountDown'
import { secondsToHHmmss, timestampToDateTime } from '@/utils/date'
import { ChapterStatus } from '@/enums/chapter'
import { ContainerWrapper, ContainerScroll } from '@/components/ContainerWrap'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import { Breadcrumb } from '@/components/ui/breadcrumb'
import {
  ArrowLeft,
  Info,
  Calendar,
  User,
  FileText,
  Clock,
  Download,
  Eye,
  Play,
  Folder,
  Tag,
  CheckCircle,
  Timer
} from 'lucide-vue-next'
import { SuperBreadcrumb } from '@/components/common'
const route = useRoute()
const router = useRouter()
const message = useMessage() // 消息弹窗
// 指引列表
const policy = ref()
const { t } = useI18n()
const fileType = ref()
const loading = ref(true)
const showVideo = ref(false)
const resqUrl = ref()
const ackBtbShow = ref(false)
const coursePlayData = computed(() => ({
  sources: [
    {
      src: formatImgUrl(resqUrl.value as string),
      type: 'video/mp4'
    }
  ]
}))
// 默认的倒计时事件
const DEFAULTSECOND = ref(180)
// 记录一个开始时间
const startTime = ref(+new Date())
// 课程当前的学习状态
const status = ref()
// 记录一个结束时间
const endTime = ref('')
const { start, count } = useCounter()

// Format date
const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Format date time
const formatDateTime = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Format file size
const formatFileSize = (bytes: number) => {
  if (!bytes) return 'N/A'
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i]
}

// Get file type label
const getFileTypeLabel = (fileType: string) => {
  const types = {
    pdf: 'PDF Document',
    mp4: 'Video File',
    doc: 'Word Document',
    docx: 'Word Document',
    ppt: 'PowerPoint',
    pptx: 'PowerPoint'
  }
  return types[fileType?.toLowerCase()] || fileType?.toUpperCase() || 'File'
}

// Get status label
const getStatusLabel = (status: number) => {
  switch (status) {
    case ChapterStatus.NotStart:
      return 'Not Started'
    case ChapterStatus.InProgress:
      return 'In Progress'
    case ChapterStatus.Completed:
      return 'Completed'
    default:
      return 'Unknown'
  }
}

// Get status variant
const getStatusVariant = (status: number) => {
  switch (status) {
    case ChapterStatus.NotStart:
      return 'secondary'
    case ChapterStatus.InProgress:
      return 'default'
    case ChapterStatus.Completed:
      return 'outline'
    default:
      return 'secondary'
  }
}

// Go back to policy list
const goBack = () => {
  router.push('/policy/center')
}

// Download single file
const downloadSingleFile = (file: any) => {
  if (file) {
    const link = document.createElement('a')
    link.href = formatImgUrl(file.fileUrl)
    link.download = file.fileName || 'download'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

/** 倒计时 */
const initData = () => {
  start(DEFAULTSECOND.value)
}

const getCompanyPolicy = async () => {
  try {
    loading.value = true
    const data = await getDetail(Number(route.params.id))
    policy.value = data
    if (data.attachmentList && data.attachmentList.length > 0) {
      fileType.value = data.attachmentList[0].fileType
      if (fileType.value === 'pdf' || fileType.value === 'PDF') {
        showVideo.value = false
        if (data.attachmentList[0].fileUrl.includes('https')) {
          axios
            .request({ baseURL: data.attachmentList[0].fileUrl, responseType: 'blob' })
            .then((abc) => {
              const blob = new Blob([abc.data], { type: 'application/pdf' })
              const url = window.URL.createObjectURL(blob)
              resqUrl.value = url
            })
        } else {
          resqUrl.value = formatImgUrl(data.attachmentList[0].fileUrl)
        }
      } else {
        resqUrl.value = data.attachmentList[0].fileUrl
        showVideo.value = true
      }
    }
  } finally {
    loading.value = false
  }
}
/** 确认已读政策 **/
const acknowledgePolicy = async () => {
  // 让用户二次确认
  const prompt = policy.value?.declaration?.replace(/\n/g, '<br />')
  await message.confirm(prompt, 'Acknowledgement')
  await editStatus({ companyPolicyId: Number(route.params.id), status: ChapterStatus.Completed })
  ackBtbShow.value = true
}

/** 查询公司政策的学习进度 */
const progress = async () => {
  if (route.query.ack === 'true') {
    const data = await searchStatus(Number(route.params.id))
    if (data) {
      status.value = data.status
      if (status.value !== ChapterStatus.Completed) {
        DEFAULTSECOND.value = data.duration
        initData()
      }
    }
  }
}
const studyStatus = watch(
  () => status.value,
  (newValue) => {
    if (newValue === ChapterStatus.NotStart) {
      studyStatus()
      /** 修改公司政策状态 */
      editStatus({ companyPolicyId: Number(route.params.id), status: ChapterStatus.InProgress })
    }
  }
)
const watcher = watch(
  () => count.value,
  (newValue) => {
    if (newValue === 3) return
    if (newValue === 0) {
      watcher()
      endTime.value = timestampToDateTime(+new Date() / 1000)
      /** 修改公司政策状态 */
      editStatus({ companyPolicyId: Number(route.params.id), status: ChapterStatus.InProgress })
    }
  }
)

onMounted(() => {
  getCompanyPolicy()
  progress()
})
</script>

<template>
  <ContainerWrapper>
    <template #content>
      <ContainerScroll>
        <!-- Header -->
        <template #header>
          <div class="flex items-center px-4 gap-3 w-full h-full">
            <!-- Left Section: Breadcrumb -->
            <div class="flex items-center gap-3">
              <SuperBreadcrumb
                current-title="Company Policy Detail"
                show-back-button
              />
              <Badge v-if="policy" variant="outline" class="text-xs font-medium">
                {{ getFileTypeLabel(fileType) }}
              </Badge>
              <Badge
                v-if="policy && status !== undefined"
                :variant="getStatusVariant(status)"
                class="text-xs font-medium"
              >
                {{ getStatusLabel(status) }}
              </Badge>
            </div>

            <!-- Center Section: Title -->
            <div class="flex-1 flex justify-center">
              <div v-if="policy" class="text-center">
                <h1 class="text-lg font-semibold text-gray-900">{{ policy.title }}</h1>
              </div>
            </div>

            <!-- Right Section: Action Buttons -->
            <div class="flex items-center gap-2">
              <!-- 倒计时显示 -->
              <div
                v-if="policy?.ack && !ackBtbShow && count > 0"
                class="flex items-center gap-2"
              >
                <Badge variant="secondary" class="text-xs font-medium">
                  <Timer class="w-3 h-3 mr-1" />
                  {{ secondsToHHmmss(count) }}
                </Badge>
              </div>

              <!-- 确认按钮 -->
              <Button
                v-if="
                  policy?.ack &&
                  !ackBtbShow &&
                  count === 0 &&
                  (status === ChapterStatus.NotStart || status === ChapterStatus.InProgress)
                "
                @click="acknowledgePolicy"
                size="sm"
                class="text-xs"
              >
                <CheckCircle class="w-4 h-4 mr-1" />
                {{ t('companypolicy.acknowledgement') }}
              </Button>
            </div>
          </div>
        </template>
        <!-- Content Area -->
        <div class="p-6">
          <!-- Loading state -->
          <div v-if="loading" class="flex items-center justify-center min-h-[400px]">
            <div class="flex flex-col items-center space-y-4">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              <span class="text-gray-600 text-lg">Loading policy details...</span>
            </div>
          </div>

          <!-- Policy Details -->
          <div v-else-if="policy">
            <!-- Basic Information -->
            <Card>
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <Info class="w-5 h-5 mr-2" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Content Description -->
                  <div v-if="policy.content" class="md:col-span-2">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Description</Label
                    >
                    <div class="mt-2 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                      <p
                        class="text-gray-700 leading-relaxed whitespace-pre-wrap"
                        v-html="policy.content.replace(/\n/g, '<br>')"
                      ></p>
                    </div>
                  </div>
                  <!-- Declaration -->
                  <div v-if="policy.declaration" class="md:col-span-2">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Policy Declaration</Label
                    >
                    <div class="mt-2 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <p
                        class="text-gray-700 leading-relaxed"
                        v-html="policy.declaration.replace(/\n/g, '<br>')"
                      ></p>
                    </div>
                  </div>

                  <!-- Department -->
                  <div v-if="policy.departmentName">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Department</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Folder class="w-4 h-4 mr-2" />
                      <span>{{ policy.departmentName }}</span>
                    </div>
                  </div>

                  <!-- Creator -->
                  <div v-if="policy.creator">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Creator</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <User class="w-4 h-4 mr-2" />
                      <span>{{ policy.creator }}</span>
                    </div>
                  </div>

                  <!-- Create Time -->
                  <div v-if="policy.createTime">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Create Time</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Calendar class="w-4 h-4 mr-2" />
                      <span>{{ formatDateTime(policy.createTime) }}</span>
                    </div>
                  </div>

                  <!-- Update Time -->
                  <div v-if="policy.updateTime">
                    <Label class="text-sm font-semibold text-gray-800 uppercase tracking-wide"
                      >Update Time</Label
                    >
                    <div class="mt-2 flex items-center text-sm text-gray-600">
                      <Clock class="w-4 h-4 mr-2" />
                      <span>{{ formatDateTime(policy.updateTime) }}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Attachments -->
            <Card class="mt-8">
              <CardHeader class="pb-4">
                <CardTitle class="text-xl flex items-center">
                  <FileText class="w-5 h-5 mr-2" />
                  Attachments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  v-if="policy.attachmentList && policy.attachmentList.length > 0"
                  class="space-y-4"
                >
                  <div
                    v-for="(attachment, index) in policy.attachmentList"
                    :key="attachment.id || index"
                    class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div class="flex items-center space-x-4 flex-1">
                      <!-- File Icon -->
                      <div class="flex-shrink-0">
                        <div
                          class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"
                        >
                          <FileText class="w-5 h-5 text-blue-600" />
                        </div>
                      </div>

                      <!-- File Info -->
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center space-x-2">
                          <h4 class="text-sm font-medium text-gray-900 truncate">
                            {{ attachment.fileName || 'Unnamed File' }}
                          </h4>
                          <Badge variant="outline" class="text-xs">
                            {{ getFileTypeLabel(attachment.fileType) }}
                          </Badge>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                          <span v-if="attachment.size">{{ formatFileSize(attachment.size) }}</span>
                          <!--                          <span v-if="attachment.duration">{{ Math.floor(attachment.duration / 60) }}:{{ String(attachment.duration % 60).padStart(2, '0') }}</span>-->
                          <!--                          <span v-if="attachment.lang">{{ attachment.lang }}</span>-->
                        </div>
                      </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center space-x-2">
                      <Tooltip>
                        <TooltipTrigger as-child>
                          <Button
                            variant="ghost"
                            size="icon"
                            @click="downloadSingleFile(attachment)"
                            class="h-8 w-8"
                          >
                            <Download class="w-4 h-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Download File</TooltipContent>
                      </Tooltip>
                    </div>
                  </div>
                </div>

                <!-- No Attachments -->
                <div v-else class="text-center py-8">
                  <div
                    class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <FileText class="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 class="text-sm font-medium text-gray-900 mb-1">No Attachments</h3>
                  <p class="text-sm text-gray-500">This policy doesn't have any attached files.</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Error state -->
          <div v-else class="flex items-center justify-center min-h-[400px]">
            <div class="text-center text-gray-500">
              <div
                class="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4 mx-auto"
              >
                <FileText class="w-12 h-12 text-slate-400" />
              </div>
              <h3 class="text-lg font-semibold text-slate-900 mb-2">Policy Not Found</h3>
              <p class="text-slate-500 max-w-md">
                The policy you're looking for doesn't exist or has been removed.
              </p>
            </div>
          </div>
        </div>
      </ContainerScroll>
    </template>
  </ContainerWrapper>
</template>

<style scoped lang="scss"></style>
