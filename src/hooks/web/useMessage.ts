import { useI18n } from './useI18n'
import { useToast } from '@/components/ui/toast'

const { toast } = useToast()

interface ConfirmState {
  isOpen: boolean
  title?: string
  description?: string
  confirmButtonText: string
  cancelButtonText: string,
  type: string
  onConfirm?: () => void
  onCancel?: () => void
}

const confirmState = ref<ConfirmState>({
  isOpen: false,
  title: '',
  description: '',
  confirmButtonText: '',
  cancelButtonText: '',
  type: '',
  onConfirm: undefined,
  onCancel: undefined
})

interface AlertState {
  isOpen: boolean
  title?: string
  description?: string
  okButtonText: string
  type: string
  onOk?: () => void
}


const alertState = ref<AlertState>({
  isOpen: false,
  title: '',
  description: '',
  okButtonText: '',
  type: '',
  onOk: undefined,
})

export const useMessage = () => {
  const { t } = useI18n()

  const closeConfirmDialog = () => {
    confirmState.value.isOpen = false
  }

  const handleConfirm = () => {
    if (confirmState.value.onConfirm) {
      confirmState.value.onConfirm()
    }
    closeConfirmDialog()
  }

  const handleCancel = () => {
    if (confirmState.value.onCancel) {
      confirmState.value.onCancel()
    }
    closeConfirmDialog()
  }

  const showToast = (message: string, type: 'info' | 'warning' | 'success' | 'error') => {
    toast({
      title: 'System hint',
      variant: type === 'error' ? 'destructive' : 'default',
      // description: `<span class="${type === 'error' ? 'text-red-900' : (type === 'success' ? 'text-green-500' : (type === 'warning' ? 'text-yellow-600' : 'text-muted-foreground'))}">${message}</span>`,
      description: message
    })
  }

  const showConfirm = (
    content: string,
    title: string,
    type?: 'info' | 'warning' | 'success' | 'error',
    confirmButtonText?: string,
    cancelButtonText?: string,
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      confirmState.value = {
        isOpen: true,
        title,
        description: content,
        type: type || 'info',
        confirmButtonText: confirmButtonText || t('common.ok'),
        cancelButtonText: cancelButtonText || t('common.cancel'),
        onConfirm: () => {
          resolve(true)
          closeConfirmDialog()
        },
        onCancel: () => {
          resolve(false)
          closeConfirmDialog()
        }
      }
    })
  }

  const closeAlertDialog = () => {
    alertState.value.isOpen = false
  }

  const handleAlert = () => {
    if (alertState.value.onOk) {
      alertState.value.onOk()
    }
    closeAlertDialog()
  }


  const showAlert = (
    content: string,
    title: string,
    type?: 'info' | 'warning' | 'success' | 'error',
    okButtonText?: string
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      alertState.value = {
        isOpen: true,
        title,
        description: content,
        type: type || 'info',
        okButtonText: okButtonText || t('common.ok'),
        onOk: () => {
          resolve(true)
          closeAlertDialog()
        }
      }
    })
  }

  return {
    alertState,
    closeAlertDialog,
    handleAlert,
    confirmState,
    closeConfirmDialog,
    handleConfirm,
    handleCancel,
    // 消息提示
    info(content: string) {
      showToast(content, 'info')
    },
    // 错误消息
    error(content: string) {
      showToast(content, 'error')
    },
    // 成功消息
    success(content: string) {
      showToast(content, 'success')
    },
    // 警告消息
    warning(content: string) {
      showToast(content, 'warning')
    },
    // 弹出提示
    alert(content: string) {
      showAlert(content, t('common.confirmTitle')).then()
    },
    // 错误提示
    alertError(content: string) {
      showAlert(content, t('common.confirmTitle'), 'error').then()
    },
    // 成功提示
    alertSuccess(content: string) {
      showAlert(content, t('common.confirmTitle'), 'success').then()
    },
    // 警告提示
    alertWarning(content: string) {
      showAlert(content, t('common.confirmTitle'), 'warning').then()
    },
    // 通知提示
    notify(content: string) {
      // ElNotification.info(content)
      showToast(content, 'info')
    },
    // 错误通知
    notifyError(content: string) {
      // ElNotification.error(content)
      showToast(content, 'error')
    },
    // 成功通知
    notifySuccess(content: string) {
      // ElNotification.success(content)
      showToast(content, 'success')
    },
    // 警告通知
    notifyWarning(content: string) {
      // ElNotification.warning(content)
      showToast(content, 'warning')
    },
    // 确认窗体
    confirm(content: string, tip?: string) {
      return new Promise((resolve) => {
        showConfirm(content, tip ? tip : t('common.confirmTitle'))
          .then(result => resolve(result))
      })
    },

    // 删除窗体
    delConfirm(content?: string, tip?: string) {
      return new Promise((resolve) => {
        showConfirm(
          content ? content : t('common.delMessage'),
          tip ? tip : t('common.confirmTitle'),
          'warning'
        )
          .then(result => resolve(result))
      })
      // return ElMessageBox.confirm(
      //   content ? content : t('common.delMessage'),
      //   tip ? tip : t('common.confirmTitle'),
      //   {
      //     confirmButtonText: t('common.ok'),
      //     cancelButtonText: t('common.cancel'),
      //     type: 'warning'
      //   }
      // )
    },
    // 删除窗体
    delAllConfirm(content?: string, tip?: string) {
      return new Promise((resolve) => {
        showConfirm(
          content ? content : t('common.delAllMessage'),
          tip ? tip : t('common.confirmTitle'),
          'warning'
        )
          .then(result => resolve(result))
      })

      // return ElMessageBox.confirm(
      //   content ? content : t('common.delAllMessage'),
      //   tip ? tip : t('common.confirmTitle'),
      //   {
      //     confirmButtonText: t('common.ok'),
      //     cancelButtonText: t('common.cancel'),
      //     type: 'warning'
      //   }
      // )
    },
    // 导出窗体
    exportConfirm(content?: string, tip?: string) {
      return new Promise((resolve) => {
        showConfirm(
          content ? content : t('common.exportMessage'),
          tip ? tip : t('common.confirmTitle'),
          'warning'
        )
          .then(result => resolve(result))
      })

      // return ElMessageBox.confirm(
      //   content ? content : t('common.exportMessage'),
      //   tip ? tip : t('common.confirmTitle'),
      //   {
      //     confirmButtonText: t('common.ok'),
      //     cancelButtonText: t('common.cancel'),
      //     type: 'warning'
      //   }
      // )
    },
    // 提交内容
    prompt(content: string, tip: string) {
      return new Promise((resolve) => {
        showConfirm(
          content ? content : t('common.exportMessage'),
          tip ? tip : t('common.confirmTitle'),
          'info'
        )
          .then(result => resolve(result))
      })

      // return ElMessageBox.prompt(content, tip, {
      //   confirmButtonText: t('common.ok'),
      //   cancelButtonText: t('common.cancel'),
      //   type: 'warning'
      // })
    }
  }
}
