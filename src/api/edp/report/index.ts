import request from '@/config/axios'

/** ----- ENUM ----- */

/** ----- INTERFACE ----- */
export interface PersonalCompletionStatusReqVO {
  year: number // 查询年份
}

export interface ContentStarTop5ReqVO {
  year: number // 查询年份
}

export interface ErrorRateTop5ReqVO {
  year: number // 查询年份
}

export interface SkillCoverageReqVO {
  year: number // 查询年份
}

export interface StudyTimeDistributionReqVO {
  year: number // 查询年份
}

export interface PersonBaseInfoReqVO {
  year: number // 查询年份
}

export interface CoreLearningMetricsReqVO {
  year: number // 查询年份
}

export interface AIGCVOReqVO {
  year: number // 查询年份
}

export interface completedCourses {
  total: number // 总数
  completedCount: number // 完成数
  completionRate: string // 完成率
}

export interface skippedStudyContent {
  total: number // 总数
  skippedCount: number // 跳过数
}

export interface PersonalCompletionStatusVO {
  requiredSkills: string[] // 所需技能
  earnedCertificates: string[] // 获得的证书
  completedCourses: completedCourses // 完成的课程
  skippedStudyContent: skippedStudyContent // 跳过的学习内容
}

export interface ContentStarTop5VO {
  skillName: string // 技能名称
  star: number // 内容评分
}

export interface ErrorRateTop5VO {
  skillName: string // 技能名称
  errorRate: number // 错误率
}

export interface PersonSkillCoverageVO {
  softSkillCoverage: Record<string, any> // 软技能掌握情况
  hardSkillCoverage: Record<string, any> // 硬技能掌握情况
  hseCoverage: Record<string, any> // HSE掌握情况
}

export interface PersonAnalysisStudyTimeVO {
  earlyMorningPercentage: string // 凌晨学习百分比
  amPercentage: string // 上午学习百分比
  pmPercentage: string // 下午学习百分比
  eveningPercentage: string // 晚上学习百分比
}

export interface PersonBaseInfoVO {
  nickname: string // 用户昵称
  badgeNumber: string // 工号
  positionName: string // 岗位名称
  deptName: string // 部门名称
}

export interface LearningEngagement {
  totalLearningDuration: string // 总学习时长
  avgHoursPerDay: string // 日均学习时长
  activeDays: number // 活跃天数
}

export interface LearningProgress {
  total: number // 总数
  completedCount: number // 完成数
  completionRate: string // 完成率
}

export interface SkillCoverage {
  total: number // 总数
  masteredCount: number // 掌握数
  masteryRate: string // 掌握率
}

export interface LearningEffectiveness {
  avgExamCorrectRate: number // 平均考试正确率
  gotCertificateCount: number // 获得证书数量
}

export interface CoreLearningMetricsVO {
  learningEngagement: LearningEngagement // 学习参与度
  learningProgress: LearningProgress // 学习进度
  skillCoverage: SkillCoverage // 技能覆盖度
  learningEffectiveness: LearningEffectiveness // 学习有效性
}

export interface SkillWeakness {
  skillName: string // 技能名称
  errorRate: number // 错误率
  completeRate: number // 完成率
  courseAccess: number // 课程点击次数
}

export interface LearningInterest {
  skillName: string // 技能名称
  rate: number // 内容评分
  tniRequest: boolean // 是否有相关TNI请求
  completeRate: number // 课程完成率
}

export interface AIGCVO {
  studyOverview: string // 学习概况
  skipAnalysis: string // 跳过分析
  skillWeakness: SkillWeakness[] // 识别技能短板——错误率、课程完成率、访问次数
  learningInterest: LearningInterest[] // 发现学习兴趣——评分、培训需求、完成率
  studyMethodSuggest: string // 学习方法分析——AI建议
  studyUpgradeSuggest: string // 学习提升建议——AI建议
}

/** ----- API ----- */
/** 获取学习要求完成情况 */
export const getPersonalCompletionStatus = async (params: PersonalCompletionStatusReqVO) => {
  return await request.appGet<PersonalCompletionStatusVO>({
    url: '/report/personal/analysis/completionStatus',
    params
  })
}

/** 获取发现学习兴趣 - 内容评分前五柱状图 */
export const getContentStarTop5 = async (params: ContentStarTop5ReqVO) => {
  return await request.appGet<ContentStarTop5VO[]>({
    url: '/report/personal/analysis/contentStarTop5',
    params
  })
}

/** 获取短板分布分析 - 岗位技能错误率 */
export const getErrorRateTop5 = async (params: ErrorRateTop5ReqVO) => {
  return await request.appGet<ErrorRateTop5VO[]>({
    url: '/report/personal/analysis/errorRateTop5',
    params
  })
}

/** 获取技能掌握分布 */
export const getSkillCoverage = async (params: SkillCoverageReqVO) => {
  return await request.appGet<PersonSkillCoverageVO>({
    url: '/report/personal/analysis/skillCoverage',
    params
  })
}

/** 获取学习时间分布分析 */
export const getStudyTimeDistribution = async (params: StudyTimeDistributionReqVO) => {
  return await request.appGet<PersonAnalysisStudyTimeVO>({
    url: '/report/personal/analysis/studyTimeDistribution',
    params
  })
}

/** 获取个人基本信息 */
export const getPersonBaseInfo = async (params: PersonBaseInfoReqVO) => {
  return await request.appGet<PersonBaseInfoVO>({
    url: '/report/personal/baseInfo',
    params
  })
}

/** 获取核心学习指标 */
export const getCoreLearningMetrics = async (params: CoreLearningMetricsReqVO) => {
  return await request.appGet<CoreLearningMetricsVO>({
    url: '/report/personal/coreLearningMetrics',
    params
  })
}

/** 获取报告中AI生成的内容 */
export const getAiGcVO = async (params: AIGCVOReqVO) => {
  return await request.appGet<AIGCVO>({
    url: '/report/personal/getAiGcVO',
    params
  })
}
