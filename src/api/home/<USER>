import request from '@/config/axios'

/** ----- ENUM ----- */
const enum studyStatusEnum {
  PENDING = 0,
  IN_PROGRESS = 1,
  COMPLETED = 2
}

export interface TodoCoursesReqVO extends PageParam {
  userId?: number // 用户 ID
  name?: string // 课程名称
  topicId?: number // 专题 ID
  parentTopicId?: number // 父级专题 ID
  subTopicIdList?: number[] // 子级专题 ID 数组
  type?: number // 类型（0：选修，1：必修）
  courseType?: number // 新课/热课类型（0：新课，1：热课）
  levelList?: number[] // 课程级别（0：所有，1：初级，2：中级，3：高级）
  languageList?: number[] // 语言（如 1：英语，2：阿拉伯语）
  subtitleList?: number[] // 字幕（1：有字幕，0：无字幕）
  sourceList?: number[] // 课程来源（1：本地，2：云端）
  durationTypeList?: number[] // 时长类型（1:<15min, 2:15-30min, 3:30-60min, 4:>60min）
}

export interface CourseAssignScopeVO {
  id: number
  relevanceId: number
  relevanceName: string
  scope: number // 范围
  type: number // 类型（0：公开，1：必修）
  topic: string
  deadline?: string // 截止时间（ISO 日期字符串）
}

export interface TodoCoursesVO {
  createTime: string // 创建时间（ISO 日期字符串）
  updateTime: string // 更新时间（ISO 日期字符串）
  creator: string
  updater: string
  deleted: boolean
  id: number // 主键 ID
  topicId: string // 专题（多个用逗号分隔的字符串）
  parentTopicId: number
  name: string
  cover: string
  keywords: string
  lang: string
  duration: number // 预估时长（秒）
  durationLower: number
  durationUpper: number
  introduction: string
  deptId: number
  status: number // 状态（0：下架，1：上架）
  shelfTime: string // 上架时间（ISO 日期字符串）
  effectiveDay: number // 课程时效（-1 表示永久）
  isAutoAssign: boolean
  isCertificateGenerated: boolean
  isRecommend: boolean
  isNew: boolean
  level: number // 课程等级
  enrollNumber: number
  pageView: number
  star: number // 总评分
  scopes: CourseAssignScopeVO[] // 课程分配信息
  myStar: number // 我的评分
  electiveNum: number
  requiredNum: number
  requiredAndCompletedNum: number
  requiredAndNotCompletedNum: number
  studyStatus: studyStatusEnum // 学习状态
  chapterNum: number
  examNum: number
  assigned: boolean
  certificateUrl: string
  topicIdList: number[]
  subTopicIdList: number[]
  language: number // 语言 (1.英语 2.阿拉伯语)
  subtitle: boolean // 是否有字幕
  source: number // 来源（1：本地，2：云端）
  handDuration: number
  handDurationLower: number
  handDurationUpper: number
  contentId: string
  assetUuid: string
  exam: number // 是否有考试
  courseIds: number[]
}

export interface TodoExamsReqVO extends PageParam {
  status?: studyStatusEnum // 状态（0：未开始，1：进行中，2：已过期）
  type?: number // 类型（0：历史，1：待办）
  onSchedule?: boolean // 是否准时
  name?: string // 名称
}

export interface TodoExamsVO {}

export interface TodoCertificatesReqVO extends PageParam {
  certificateId?: string // 证书ID,示例值(19591)
  userId?: string // 用户ID,示例值(29812)
  type?: number // 1:课程学习 2:完成任务 3:参加培训 4:手动颁发 5:参加考试,示例值(2)
  number?: string // 证书编号
  isRevoke?: number // 是否吊销 1=已吊销 2=未吊销
  validity?: number // 有效期month
  expiresTime?: string // 过期时间
  image?: string // 证书地址
  taskId?: string // 关联id,示例值(14647)
  createTime?: string // 创建/获取时间
}

export interface getCompanyPoliciesReqVO extends PageParam {
  title?: string // 标题
  status?: number // 状态（0：Not started，1：In progress，3：Completed）
}

export interface TodoCompanyPoliciesVO {
  id: number // 主键ID
  departmentId: number // 部门id
  departmentName: string // 部门名称
  title: string // 标题
  cover: string // 封面图
  declaration: string // 声明
  content: string // 内容
  ack: boolean // 是否确认
  sort: number // 排序码
  fileTypeList: string[] // 文件类型列表
  status: number // 状态（0：Not started，1：In progress，3：Completed）
  status_: string // 状态（0：Not started，1：In progress，3：Completed）
  createTime: string // 创建时间
  updateTime: string // 更新时间
  creator: number // 创建人
  updater: number // 更新人
}

export interface getTodoJourneysReqVO extends PageParam {
  categoryId?: number // 分类ID
  title?: string // 标题
  journeyIds?: string // 学习地图ID列表
}

export interface CoursesList {
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
  id: number
  topicId: string
  parentTopicId: number
  name: string
  cover: string
  keywords: string
  lang: string
  duration: number
  durationLower: number
  durationUpper: number
  introduction: string
  deptId: number
  status: 0 | 1
  shelfTime: string
  effectiveDay: number
  autoAssign: boolean
  isRecommend: boolean
  isNew: boolean
  level: 0 | 1 | 2 | 3
  isCertificateGenerated: boolean
  enrollNumber: number
  pageView: number
  star: number
  delete: boolean
  scopes: CourseAssignScopeVO[]
  topic: string
  type: 0 | 1 // 0: 选修, 1: 必修
  deadline: string
  myStar: number
  electiveNum: number
  requiredNum: number
  requiredAndCompletedNum: number
  requiredAndNotCompletedNum: number
  studyStatus: 0 | 1 | 3 // 0: 待学习, 1: 学习中, 3: 已完成
  chapterNum: number
  examNum: number
  assigned: boolean
  certificateUrl: string
  topicIdList: number[]
  subTopicIdList: number[]
  language: 1 | 2 // 1: 英语, 2: 阿拉伯语
  subtitle: boolean
  source: 1 | 2 // 1: 本地, 2: 云端
  handDuration: number
  handDurationLower: number
  handDurationUpper: number
  contentId: string
  assetUuid: string
  exam: 0 | 1
  courseIds: number[]
}

export interface TodoJourneysVO {
  id: number
  title: string
  status: 0 | 1 // 0: 下架, 1: 上架
  cover: string
  courseIds: string
  keywords: string
  introduction: string
  courseList: CoursesList[]
  courseCount: number
  courseDuration: string
  studentCount: number
  completed: boolean
}

export interface TodoLiveReqVO extends PageParam {
  name?: string // 直播名称
  statusList?: number[] // 直播状态
  startTime?: string // 直播开始时间
  sortRule?: number // 排序规则 1.最新 latest 2.最热 hot
  userId?: number // 用户ID
}

export interface Speakers {
  userId: number
  nickname: string
  avatar: string
  deptId: number
  deptName: string
  joinType: 10 | 20 // 10.预约入会 20.邀请入会
  role: 10 | 20 // 10.普通参与者 20.主讲人
  attendance: boolean
}

export interface TodoLivesVO {
  id: number
  cover: string
  name: string
  userId: number
  nickname: string
  status: 10 | 20 | 50 | 90 // 10.待发布 20.待直播 50.直播中 90.已结束
  statusName: string
  privacy: 1 | 2 // 1.公开 2.私密
  startTime: string
  createTime: string
  actualStartTime: string
  speakers: Speakers[]
  reservationTotal: number
  reserved: boolean
  joinType: 10 | 20 // 10.预约入会 20.邀请入会
  joinTypeName: string
  liveLinkUrl: string
  favourite: boolean
}

export interface TodoSurveysReqVO extends PageParam {
  name?: string // 问卷名称
  categoryId?: number // 问卷分类ID
}

export interface Questions {
  id: number;                    // 主键ID
  questionType: number;          // 问题类型
  questionTypeName: string;      // 问题类型名称
  title: string;                 // 问题标题
  description: string;           // 问题描述
  required: boolean;             // 是否必答
  sort: number;                  // 排序字段
  config: object;                // 问题配置
}

export interface TodoSurveysVO {
  id: number;                    // 主键ID
  name: string;                  // 问卷名称
  description: string;           // 问卷描述
  creatorId: string;             // 创建者ID
  creatorName: string;           // 创建者名称
  deptId: number;                // 归属部门ID
  deptName: string;              // 归属部门名称
  startTime: string;             // 开始时间
  endTime: string;               // 结束时间
  status: number;                // 状态
  statusName: string;            // 状态名称
  responseCount: number;         // 响应数量
  maxResponses: number;          // 最大响应数
  anonymousEnabled: boolean;     // 是否允许匿名参与
  submissionFrequency: number;   // 提交频率
  submissionFrequencyName: string; // 提交频率名称
  maxSubmissions: number;        // 最大提交次数
  allowViewStatistics: boolean;  // 是否允许用户查看统计数据
  config: object;                // 问卷配置
  questions: Questions[]; // 问题列表
  canParticipate: boolean;       // 用户是否可以参与
  canSubmit: boolean;            // 用户是否可以提交
  userSubmissionCount: number;   // 用户已提交次数
}

export interface CoordinateDetails {
  nameDetails: Coordinate // 姓名坐标详情
  logoDetails: Coordinate // logo坐标详情
  numberDetails: Coordinate // 编号坐标详情
  officialSealDetails: Coordinate // 公章坐标详情
  userDetails: Coordinate // 用户坐标详情
  timeDetails: Coordinate // 时间坐标详情
}

export interface Coordinate {
  x: number
  y: number
}

export interface TodoCertificatesVO {
  id: number // 主键id
  certificateId: number // 证书id
  userId: number // 用户id
  type: number // 1:课程学习 2:完成任务 3:参加培训 4:手动颁发 5:参加考试
  number: string // 证书编号
  isRevoke: number // 是否吊销 1=已吊销 2=未吊销
  validity: number // 有效期month
  expiresTime: string // 过期时间
  image: string // 证书地址
  taskId: number // 关联id
  createTime: string // 创建/获取时间
  name: string // 证书名称
  numberPrefix: string // 编号前缀
  logo: string // logo
  officialSeal: string // 公章
  certificateImage: string // 证书模板图片
  coordinateDetails: CoordinateDetails // 坐标相关详情
}

/** ----- API ----- */
/** 获取轮播 */
export const getBannerCarousels = async (params: any) => {
  return await request.get({
    url: 'http://10.248.18.22:48080/admin-api/system/banner',
    params
  })
}

/** 获取统计信息 */
export const getStatistics = async () => {
  return await request.appGet({
    url: '/learning/home/<USER>'
  })
}

/** 获取新闻详情 */
export const getNews = async (id: number) => {
  return await request.get({
    url: `http://10.248.18.22:48080/admin-api/system/banner/${id}`
  })
}

/** 获取我的任务数量 */
export const getMyTaskNum = async () => {
  return await request.appGet({
    url: '/learning/home/<USER>'
  })
}

/** 获取待办课程 */
export const getTodoCourses = async (params: TodoCoursesReqVO) => {
  return await request.appGet({
    url: '/learning/course/todoList',
    params
  })
}

/** 获取待办考试 */
export const getTodoExams = async (params: TodoExamsReqVO) => {
  return await request.appGet({
    url: '/learning/exam/my',
    params
  })
}

/** 获取待办公司政策 */
export const getTodoCompanyPolicies = async (params: getCompanyPoliciesReqVO) => {
  return await request.appGet({
    url: '/learning/company-policy/todo/page',
    params
  })
}

/** 获取待办学习地图 */
export const getTodoJourneys = async (params: getTodoJourneysReqVO) => {
  return await request.appGet({
    url: '/learning/journey/todo/page',
    params
  })
}

/** 获取待办直播 */
export const getTodoLives = async (params: TodoLiveReqVO) => {
  return await request.appGet({
    url: '/live/room/participated-page',
    params
  })
}

/** 获取待办问卷 */
export const getTodoSurveys = async (params: TodoSurveysReqVO) => {
  return await request.appGet({
    url: '/system/survey/my-todo',
    params
  })
}

/** 获取证书 */
export const getTodoCertificates = async (params: TodoCertificatesReqVO) => {
  return await request.appGet({
    url: '/system/certificate-user/page',
    params
  })
}
