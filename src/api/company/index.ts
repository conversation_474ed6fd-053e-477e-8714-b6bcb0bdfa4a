import request from '@/config/axios'

/** 公司政策查询参数 */
export interface CompanyPolicyNavReqVO {
  /** 页码，从 1 开始 */
  pageNo: number
  /** 每页条数，最大值为 100 */
  pageSize: number
  /** 标题 */
  title?: string
  /** 状态（0：Not started，1：In progress，3：Completed） */
  status?: string
}

/** 公司政策附件响应对象 */
export interface CompanyPolicyAttachmentRespVO {
  /** 主键 */
  id: number
  /** companyPolicyId */
  companyPolicyId: number
  /** 0：本地，1：资源库 */
  origin: number
  /** 资源id */
  resourceId: number
  /** 文件id */
  fileId: number
  /** 文件地址 */
  fileUrl: string
  /** 文件名称 */
  fileName: string
  /** 文件类型 */
  fileType: string
  /** 媒体类型（1：视频，2：音频，3：文档，4：SCORM） */
  mediaType: number
  /** 语言 */
  lang: string
  /** 大小 */
  size: number
  /** video播放总时长 */
  duration: number
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建人 */
  creator: number
  /** 更新人 */
  updater: number
}

/** 公司政策响应对象 */
export interface CompanyPolicyRespVO {
  /** 主键ID */
  id: number
  /** 部门id */
  departmentId: number
  /** 部门名称 */
  departmentName: string
  /** 标题 */
  title: string
  /** 封面图 */
  cover: string
  /** 声明 */
  declaration: string
  /** 内容 */
  content: string
  /** 关键词 */
  keywords: string
  /** 来源，0：本地，1：资源库 */
  origin: number
  /** 格式 */
  format: string
  /** 媒体类型（1：视频，2：音频，3：文档，4：SCORM） */
  mediaType: number
  /** 语言 */
  lang: string
  /** 预估时间，秒 */
  duration: number
  /** 时长下限 */
  durationLower: number
  /** 时长上限 */
  durationUpper: number
  /** 是否确认 */
  ack: boolean
  /** 排序码 */
  sort: number
  /** 附件列表 */
  attachmentList: CompanyPolicyAttachmentRespVO[]
  /** 文件类型列表 */
  fileTypeList: string[]
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 创建者ID */
  creator: string
  /** 创建人 */
  createBy: string
  /** 创建时间 */
  creationTime: string
  /** 更新人 */
  updater: number
}

/** 分页结果 */
export interface PageResultCompanyPolicyRespVO {
  /** 数据列表 */
  list: CompanyPolicyRespVO[]
  /** 总量 */
  total: number
}

/** 向后兼容的类型别名 */
export interface CompanyPolicyListVO extends CompanyPolicyRespVO {}
export interface AppCompanyPolicyPageRespVO extends CompanyPolicyRespVO {}

export interface CompanyPolicyUpdateVO {
  companyPolicyId: number
  status: number
}

/**
 * 导航 查询
 * @param params
 * @returns
 */
export const companyPolicyList = async (params: CompanyPolicyNavReqVO) => {
  return await request.get<CompanyPolicyListVO[]>({
    url: '/learning/company-policy/nav/page',
    params
  })
}
/**
 * My-todo 查询
 * @param params
 * @returns
 */
export const companyPolicyTodoList = async (params: CompanyPolicyNavReqVO) => {
  return await request.get({
    url: '/learning/company-policy/todo/page',
    params
  })
}
/**
 * 个人中心-分页查询公司政策
 * @param params 查询参数
 * @returns 分页结果
 */
export const companyPolicyCenterList = async (params: CompanyPolicyNavReqVO) => {
  return await request.appGet<PageResultCompanyPolicyRespVO>({
    url: '/learning/company-policy/nav/page',
    params
  })
}
/** Company Policy对应的详情页面 **/
export const getDetail = async (id: number) => {
  return await request.get({
    url: `/learning/company-policy/get?id=${id}`
  })
}
/** 修改公司政策状态 */
export const editStatus = async (data: CompanyPolicyUpdateVO) => {
  return await request.put({
    url: `/learning/company-policy/record/status`,
    data
  })
}
/** 查询状态 */
export const searchStatus = async (companyPolicyId: number) => {
  return await request.get({
    url: `/learning/company-policy/record/${companyPolicyId}`
  })
}
