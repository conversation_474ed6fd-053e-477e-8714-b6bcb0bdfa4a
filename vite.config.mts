import {resolve} from 'path'
import type {ConfigEnv, UserConfig} from 'vite'
import {loadEnv} from 'vite'
import {createVitePlugins} from './build/vite'
import {exclude, include} from "./build/vite/optimize"
// import tailwindcss from "@tailwindcss/vite/dist";
// import tailwindcss from "@tailwindcss/vite";
// import autoprefixer from 'autoprefixer'
// import tailwind from 'tailwindcss'
// 当前执行node命令时文件夹的地址(工作目录)
const root = process.cwd()

// 路径查找
function pathResolve(dir: string) {
    return resolve(root, '.', dir)
}

// https://vitejs.dev/config/
export default ({command, mode}: ConfigEnv): UserConfig => {
    let env = {} as any
    const isBuild = command === 'build'
    if (!isBuild) {
        env = loadEnv((process.argv[3] === '--mode' ? process.argv[4] : process.argv[3]), root)
    } else {
        env = loadEnv(mode, root)
    }
    return {
        base: env.VITE_BASE_PATH,
        root: root,
        // 服务端渲染
        server: {
            port: env.VITE_PORT,
            host: '0.0.0.0',
            https: false,
            open: env.VITE_OPEN === 'true',
            proxy: {
                '/minio-storage': {
                    target: env.VITE_MINIO_STORAGE,
                    ws: false,
                    changeOrigin: true,
                    secure: false,  // 添加此项以避免 connect ECONNREFUSED
                    rewrite: (path) => path.replace(new RegExp(`^/minio-storage`), ''),
                },
                '/app-api': {
                    target: 'http://************:48080', // 目标服务器地址
                    ws: false,
                    changeOrigin: true,
                    secure: false, // 添加此项避免 ECONNREFUSED
                    // 不再重写路径，保留原始路径中的 `/app-api`
                },
            },
        },

        // 项目使用的vite插件。 单独提取到build/vite/plugin中管理
        plugins: createVitePlugins(),
        // css: {
        //     // preprocessorOptions: {
        //     //     scss: {
        //     //         additionalData: '@use "@/styles/variables.scss" as *;',
        //     //         javascriptEnabled: true
        //     //     }
        //     // },
        //   postcss: {
        //     plugins: [tailwind(), autoprefixer()],
        //   },
        // },
        resolve: {
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'],
            alias: [
                {
                    find: 'vue-i18n',
                    replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
                },
                {
                    find: /\@\//,
                    replacement: `${pathResolve('src')}/`
                }
            ]
        },
        build: {
            minify: 'terser',
            outDir: env.VITE_OUT_DIR || 'dist',
            sourcemap: env.VITE_SOURCEMAP === 'true' ? 'inline' : false,
            // brotliSize: false,
            terserOptions: {
                compress: {
                    drop_debugger: env.VITE_DROP_DEBUGGER === 'true',
                    drop_console: env.VITE_DROP_CONSOLE === 'true'
                }
            },
            rollupOptions: {
                output: {
                    manualChunks: {
                        echarts: ['echarts'] // 将 echarts 单独打包，参考 https://gitee.com/yudaocode/yudao-ui-admin-vue3/issues/IAB1SX 讨论
                    }
                },
            },
        },
        optimizeDeps: {include:['aplayer'], exclude},
        logLevel: 'info',  // 显示信息、警告和错误信息，这样可以看到服务器启动地址
    }
}